/*!
 * Propeller v1.0.0 (http://propeller.in)
 * Copyright 2016-2017 Digicorp, Inc.
 * Licensed under MIT (http://propeller.in/LICENSE)
 */
body, html{ height:100%;}
.pmd-content{ min-height:100%;}
.dashboard .card-header.no-margin { margin:0;}
.dashboard .pmd-card{ margin-bottom:30px;}
.caret { border-left:5px solid rgba(0, 0, 0, 0); border-right:4px solid rgba(0, 0, 0, 0); border-top:5px solid;}
.pmd-navbar.navbar .btn.pmd-btn-fab:hover, .pmd-navbar.navbar .btn.pmd-btn-fab:focus, .pmd-navbar.navbar .btn.pmd-btn-fab.focus { color: #fff;}

/*========================== header css ===========================*/
.navbar-inverse{ background-color:#4285f4; border-color:#4285f4;}
.navbar-inverse .navbar-brand svg{ display:inline-block; float:left; vertical-align:top;}
@media (max-width: 767px) {
	.navbar-brand > svg{ height:40px; width:auto;}
}
	
/*============================= notification =========================*/
.notification .pmd-badge[data-badge]::after{ background-color:#fff; color:#333;}
.notification .pmd-dropdown-menu-container { top:65px;}
.notification.open .pmd-dropdown-menu-container:after{ border-bottom: 6px solid #ffffff; border-left: 6px solid rgba(0, 0, 0, 0);border-right: 6px solid rgba(0, 0, 0, 0); content: ""; display: inline-block; right: 24px; position: absolute; top: -6px;}
.notification .notification-blank { padding:120px 16px; text-align:center; opacity:0.7;}
nav .notification .notification-blank span.dic { display:block; font-size:58px; margin-bottom:10px; opacity:0.3;}
.notification .dropdown-menu { padding:0; width:320px; position:relative;}
.notification a.dropdown-toggle{ margin-top:25px;}
.notification a{ position:relative; overflow:visible;}
.notification .list-group-item { background:#F9F9F9; line-height:15px; cursor:pointer; float:left; width:100%;}
.notification .list-group-item { padding:0; border-top:1px solid #ebebeb; margin:0;}
.notification .list-group-item a { border:0;}
.notification .list-group-item .avatar-list-img40x40 { margin-top:0;}
.notification .list-group-item .list-group-item-text { line-height:1.4; display:block; white-space:normal; font-size:14px;}
.notification .list-group-item .list-group-item-date { color:rgba(0, 0, 0, 0.44); font-size:13px;  display:block;}
.notification .list-group-item > a { padding:16px; right:0; margin-top:0;}
.notification .list-group-item.unread { background:#fff;}
.notification .list-group-item:hover { background:#F5F5F5;}
.pmd-navbar .navbar-nav > li.notification svg { margin-right:0;}
.notification .nav-dropdown h3 a { text-decoration:underline; margin-top:0;}
.notification .nav-dropdown h3 a:hover { text-decoration:none;}
.avatar-list-img40x40{width:40px;height:40px;border-radius:50%;display:table-cell;overflow:hidden;vertical-align:middle;position:relative; text-align:center; }
.avatar-list-img40x40 .dic { vertical-align:middle;}
.avatar-list-img40x40 {width:40px;height:40px;border-radius:50%;display:inline-block;overflow:hidden;vertical-align:middle;position:relative; line-height: 38px;} 
.avatar-list-img40x40 img {max-height: 40px; width: auto;  height: auto; max-width: 40px; display:inline-block;}
.avatar-list-img140x140{width:140px;height:140px; border-radius:50%;display:inline-block;overflow:hidden;vertical-align:middle;position:relative; text-align:center; line-height:140px; margin:0 auto;}
.avatar-list-img140x140::after {border-radius: 50%;box-shadow: 0 0 2px rgba(0, 0, 0, 0.3) inset;content: "";height: 140px;left: 0;position: absolute;top: 0;width: 140px;z-index: 100;}
.avatar-list-img140x140 img {max-height: 140px; width: auto;  height: auto; max-width: 140px; display:inline-block;}
.notification .pmd-list-avatar{ display:inline;}
@media (max-width: 767px) {
.notification a.dropdown-toggle{margin-top: 18px;}	
}

/*============================= content area =========================*/
.pmd-content{padding-top: 63px; padding-bottom:90px; padding-left:0; padding-right:0;}
.pmd-content .container-fluid { max-width:1590px;}
@media (max-width: 767px) {
.pmd-content{padding-top: 63px; padding-left:0; padding-bottom:210px; padding-right:0;}
}
/*============================== dashboard common css ===============================*/
.dashboard { margin-top:30px;  animation:fadeIn cubic-bezier(0.55, 0, 0.1, 1) 0.8s; -webkit-animation:fadeIn cubic-bezier(0.55, 0, 0.1, 1) 0.8s; -ms-animation:fadeIn cubic-bezier(0.55, 0, 0.1, 1) 0.8s; -o-animation:fadeIn cubic-bezier(0.55, 0, 0.1, 1) 0.8s; -moz-animation:fadeIn cubic-bezier(0.55, 0, 0.1, 1) 0.8s; padding-right:15px; padding-left:15px;}
@media (max-width: 767px) {
.dashboard{margin-top: 8px; padding-left: 0; padding-right: 0;}
}

/* Dashboard title icon */
.dashboard .service-icon { width:40px; height:40px; display:block; position:relative;}
.dashboard .service-icon img, .dashboard .service-icon svg { position:absolute; left:0; right:0; top:0; bottom:0; margin:auto; width:20px;}
.dashboard .row > div{ padding-left: 10px; padding-right: 10px;}

/* Dashboard card*/
.dashboard .pmd-card-title {font-size: 18px; margin-top:0; margin-bottom:0;}
.dashboard .pmd-card { margin-bottom:20px;}
.dashboard .pmd-card-body, .dashboard .notification .content-section { min-height:100px;}
h2.typo-fill-secondary{ color:rgba(77, 87, 93, 0.68); font-size:22px;}
.dashboard .pmd-display2{ opacity:1; font-weight:500;}
.grow-up{ margin-left:5px;}

/*======= Breadcrumb =======*/
.breadcrumb { background: transparent; font-size:14px; margin:0 0 20px 0px; padding:0;}

/*================= site activity css =========================*/
/*total sales circle chart css*/
.total-sales { text-align:center; display:inline-block; width:100%;}
.total-sales .chart{ width:50%;}
.total-sales .chart.circle-chart{ padding-right:3%;}
.total-sales .chart.total-revenue{padding-left:3%;}
.total-sales .circle { display: inline-block; margin:0 10px 0 10px; position:relative;}
.total-sales .circles-wrp { margin-bottom:6px;}
.total-sales .circles-integer { font-size:20px; font-family: 'Roboto'; display:block; font-weight:700;}
.total-sales .border-right {border-right:1px solid #dfe3e7;}
.total-sales .chart-title {line-height: 1.1; padding: 0 15px; width: 99px; margin:auto; font-weight:500;}
.total-sales .circle .circles-text { height:auto !important;}
.total-sales .circles-integer:before { content:""; display:inline-block; height:19px; vertical-align:middle; background-size:11px;}
/* total sales progressbar chart css*/
.total-revenue .progress { height:8px; margin:0; border-radius:10px; background:#DFE3E7; box-shadow:none;}
.total-revenue ul { margin:0;}
.total-revenue .list-group-item{ padding:1px 0; display:table; margin:0 auto;}
.total-revenue .media-body { vertical-align:middle;  max-width:175px;}
.total-revenue .media-left { font-size:16px; font-family:'Roboto'; text-align:left; color: #888888; font-weight:500;}
.total-revenue .media-right { font-size:20px; font-family: 'Roboto'; text-align:left; padding-left:20px; min-width:110px; font-weight:700;}
.total-revenue .progress .progress-bar { height: 100%; width: 0; background-color:#F7912F; color:#F7912F;}
@media (min-width: 991px) and (max-width: 1600px){
.total-sales .chart.circle-chart{ padding-right:3%; width:60%;}
.total-sales .chart.total-revenue{padding-left:3%; width:40%;}
}
@media (max-width: 990px) {
.total-sales .chart{ width:100%;}
.total-sales .circle{ margin-bottom:20px;}
.total-sales .chart.total-revenue{ margin-top:20px;}
.total-sales .border-right{ border:none;}
}

/*===================== marketplace card css new ============================*/
.advertising-info{ min-height:262px;}
@media (max-width: 1360px) {
.advertising-info .pmd-display2{ font-size:20px;}
}
@media (max-width: 990px) {
.advertising-info{ min-height:inherit;}	
}
	
/*=================== range date picker =====================*/
.daterangepicker.dropdown-menu {opacity:1; -webkit-transition: none; -moz-transition: none; -o-transition: none; transition: none;  -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.16), 0 1px 3px 0 rgba(0, 0, 0, 0.12); -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.16), 0 1px 3px 0 rgba(0, 0, 0, 0.12); box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.16), 0 1px 3px 0 rgba(0, 0, 0, 0.12); clip: initial; }
.range-calendar {cursor:pointer; position:absolute; right:30px; max-width: 260px;}
.range-calendar svg:hover path { fill: #4d575d !important;}
@media (max-width: 767px) {
.datetimepicker{ display:none;}	
}

/*====================== browser usage ================*/
.value-added .value-added-section { border-right:1px solid #dfe3e7;  border-bottom:1px solid #dfe3e7; padding-top:14px; padding-bottom:13px;}
.value-added .value-added-section .title { font-size:18px; line-height:1.2; color:rgba(77, 87, 93, 0.68); font-weight:500;}
.value-added .value-added-section:nth-child(2), .value-added .value-added-section:nth-child(4),.value-added .value-added-section:nth-child(6) { border-right:0;}
.value-added .value-added-section:nth-child(5), .value-added .value-added-section:nth-child(6) { border-bottom:0;}
.pt10 { padding-top:10px;}

/*========================== statistics card =======================*/
.statistics { padding:0 10px;}
.statistics li{ width:32.68%; vertical-align:top; margin-bottom:20px;}
.statistics .statistic-img-circle { width:64px; height:64px; border:4px solid #3cad44; border-radius:50%; margin:0 auto 8px;}
.statistics img, .statistics svg { max-width:57px; margin:0 auto; height:57px;}
.statistics .pmd-display2 { font-size: 28px; line-height:1.2;}
.statistics .typo-fill-secondary {color: rgba(77, 87, 93, 0.68);font-weight: 500;}
.users-details-info{ margin-bottom:15px; float:left; width:30%;}
.users-details-chart{margin: auto; min-width: 70%; width:70%; float:right; height:300px;}
@media (max-width: 767px) {
.statistics li{width: 48%;}
}

/*======================== user details ====================*/
.sms-details{min-height: 402px;}
.sms-details .media-right { width:15%;}
.sms-details .media-right a { margin: 0 5px;}
.sms-details .card-header svg:hover path, .sms-details .card-header svg:hover rect, .sms-details .card-header svg:hover ellipse { fill:#4d575d;}
.sms-details .card-header .service-icon svg:hover path, .sms-details .card-header .service-icon svg:hover rect{ fill: #fff;}
.sms-details .pmd-display2 { margin-top:0;}
@media (max-width: 1360px) {
.users-details-info{ width:100%;}
.users-details-chart{ width:100%; height:200px;}
}
@media (max-width: 767px) {
.sms-details{min-height: 445px;}	
}

/*====================== project progress css ====================*/
.project-progress .content-section ul { margin:10px 0 0 0;}
.project-progress .timeline { padding-left:50px; border-bottom:1px solid #ebebeb; width:100%; margin:0;  padding-left: 50px; padding-top: 15px;}
.project-progress .timeline:last-child { border: 0;}
.project-progress .timeline:before { background:#fff; content:""; position:absolute; top:0; bottom:0; left:25px; height:101%; width:2px; display:block;}
.project-progress .timeline:after { background:#fff; border-radius:50% !important; content:""; display:block; height:12px; left:25px; margin-left:-5px; margin-top:-6px; position:absolute; top:50%; width:12px; z-index:2;}
.project-progress .timeline.project-notification:before { background:#df6c6c !important;}
.project-progress .timeline.project-notification:after { background:#df6c6c !important;}
.project-progress .timeline.project-info:before { background:#67b951 !important;}
.project-progress .timeline.project-info:after { background:#67b951 !important;}
.project-progress li h5.typo-fill-secondary{ color:#999; font-size:12px; margin:0; margin-top:4px;}

/*===================== recent post css ========================*/
.recent-post ul{ margin-top:0;}
.recent-post .card-body{ min-height:289px;}
.reload{ float:right;}
.reload:hover svg path{ fill: #4d575d;}
.pmd-card-list .post{ width: 24%; text-align:right;}
.post-time{ font-size:11px; color:#a5a4a4;}
@media (max-width: 767px) {
.pmd-card-list .post { display: block; line-height: 1; padding-left: 58px; text-align: left; width: 100%;}
}

/*footer*/
.admin-footer { background: #fff none repeat scroll 0 0; border-top: 1px solid rgba(0, 0, 0, 0.1); padding-bottom: 14px; padding-top: 17px; transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1) 0s; margin-top:-70px; position:relative; z-index:1;}
.pmd-sidebar ~ .admin-footer { padding-left: 64px; }
.pmd-sidebar.pmd-sidebar-open ~ .admin-footer { padding-left: 290px;}
.copyright-text { color: #4d575d; font-size: 14px; padding-top: 8px;}
.copyright-text a:hover { color: #4285f4;}
.admin-footer a {color: #4d575d; display: inline-block;}
.admin-footer .pmd-card-subtitle-text {font-size: 12px; line-height: 18px; opacity: 1; margin: 0px;}
.admin-footer .media-left { padding-left: 0; padding-right: 10px;}
.admin-footer .media-left svg { margin-top: 4px;}
.admin-footer .pmd-card-title-text a { color: #fff;}
.admin-footer a:hover svg path, .admin-footer a:focus svg path { fill: #4d575d;}
.admin-footer a:hover { color: #4d575d;}
.admin-footer .list-inline{ line-height:1.2;}
.admin-footer .download-now, .admin-footer .for-support{max-width: 240px; padding:0 15px;}
.admin-footer .download-now a > div, .admin-footer .for-support a > div,
.admin-footer .download-now a > div svg, .admin-footer .for-support a > div svg{ display:inline-block; vertical-align:middle; line-height:1;}
.admin-footer .download-now a > div svg, .admin-footer .for-support a > div svg{ margin-right:10px;}
.admin-footer ul{ margin:0;}

@media (max-width: 767px) {
.admin-footer{ margin-top: -190px;}
.pmd-sidebar ~ .admin-footer{padding-left:0;}
/*footer*/
.copyright-text { text-align: center;}
.admin-footer .media-left{ display:block;}
.admin-footer a{ text-align:center;}
.admin-footer .list-inline > li{ display:block; padding:8px 0;}
.admin-footer a{ text-align:left;}
.admin-footer .download-now, .admin-footer .for-support{ float:none; max-width:100%; width:100%;}
}
	
@keyframes fadeIn {
0%{transform:translate(0, 40px);-webkit-transform:translate(0, 40px);opacity:0;}
100%{transform:translate(0, 0);-webkit-transform:translate(0, 0);opacity:1;}
}
@-webkit-keyframes fadeIn {
0%{transform:translate(0, 40px);-webkit-transform:translate(0, 40px);opacity:0;}
100%{transform:translate(0, 0);-webkit-transform:translate(0, 0);opacity:1;}
}
@-moz-keyframes fadeIn {
0%{transform:translate(0, 40px);-webkit-transform:translate(0, 40px);opacity:0;}
100%{transform:translate(0, 0);-webkit-transform:translate(0, 0);opacity:1;}
}

/*=============================For Loader========================*/
.btn-loader.loader { opacity:1; margin: auto; font-size: 0.3em; position: absolute; text-indent: -9999em; border-top: 1em solid rgba(226, 60, 60, 0.2); border-right: 1em solid rgba(226, 60, 60, 0.2); border-bottom: 1em solid rgba(226, 60, 60, 0.2); border-left: 1em solid #E23C3C; -webkit-transform: translateZ(0); -ms-transform: translateZ(0); transform: translateZ(0); -webkit-animation: load8 1.1s infinite linear; animation: load8 1.1s infinite linear; top:0; right:0; left:0; bottom:0; overflow:hidden;}
.btn-loader.loader,
.btn-loader.loader:after { border-radius: 50%; width: 7em; height: 7em;}

@-webkit-keyframes load8 { 0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

/*=========================faqs=========================*/
.faqs-section{height:100%; color:#333;}
.faqs-section h3 { margin-bottom: 50px;}
.faqs-section .list-group-item.active, .faqs-section .list-group-item.active:hover, .faqs-section .list-group-item.active:focus{background:transparent;}
.faqs-section .list-group-item.active a, .faqs-section .list-group-item.active:hover a, .faqs-section .list-group-item.active:focus a, .faqs-section .list-group-item a:hover, .faqs-section .list-group-item a:focus {color: #4285f4;}
.faqs-section h5{color:rgb(68, 68, 68); margin-bottom:50px; margin-top:50px}
.faqs-section p { color:rgb(119, 119, 119); font-size:18px;}
.faqs-section p strong { font-weight:600;}
.faqs-section .list-group-item.active .dic {color: #4285f4;}

/*Accordion*/
.faqs-section .panel{ background-color:transparent; text-align:left; box-shadow:none; border:0;}
.faqs-section .panel-heading{ background-color:transparent;}
.faqs-list{ margin:30px;}
.faqs-section .panel-title{text-transform:none; font-size:18px; line-height: 24px;}
.faqs-section .dic.media-left{ padding-right: 19px; line-height:1; font-size:22px;}
.faqs-section .panel-group .panel-heading + .panel-collapse > .panel-body, .faqs-section .panel-group .panel-heading + .panel-collapse > .list-group{ border:none; padding-left:55px; padding-bottom:0; padding-top:0;}

/*===============================sidebar with icons=============================*/
.pmd-sidebar .pmd-sidebar-nav li a { color:#c8c8c8; font-size:15px; font-family:"Roboto"; padding: 18px 24px; transition: all 0.5s ease; -moz-transition: all 0.5s ease; -webkit-transition: all 0.5s ease;}
.pmd-sidebar .pmd-sidebar-nav li a i { min-width:44px; text-align:center;}
.pmd-sidebar .pmd-sidebar-nav a i svg {display: block; margin: 0 auto;}
.pmd-sidebar-left .nav > li > a:hover, .pmd-sidebar-left .nav > li > a:focus, .pmd-sidebar-left .nav > li > a:active, .pmd-sidebar-left .nav > li > a.active { background-color:#1a2940; color:#fff;}
.pmd-sidebar-left .nav > li > a:hover svg path, .pmd-sidebar-left .nav > li > a:focus svg path, .pmd-sidebar-left .nav > li > a:active svg path, .pmd-sidebar-left .nav > li > a.active svg path, .pmd-sidebar-left .nav > li > a:hover svg, .pmd-sidebar-left .nav > li > a:focus svg, .pmd-sidebar-left .nav > li > a:active svg, .pmd-sidebar-left .nav > li > a.active svg  { fill: #fff;}
.pmd-sidebar.pmd-sidebar-open{ width:274px; min-width:274px;}
.pmd-sidebar-slide-push.pmd-sidebar-open ~ .wrapper .constructor, .pmd-sidebar-slide-push.pmd-sidebar-open ~ .pmd-content { margin-left:274px;}
.pmd-sidebar-left .nav > li.pmd-user-info > a { border-bottom:solid 1px #1a2940;  font-size:16px; color:#fff; padding-left:12px;}
.pmd-sidebar-left .nav > li.pmd-user-info > a:hover, .pmd-sidebar-left .nav > li.pmd-user-info > a:focus, .pmd-sidebar-left .nav > li.pmd-user-info > a:active, .pmd-sidebar-left .nav > li.pmd-user-info > a.active { background:#1a2940;}
.pmd-sidebar .pmd-sidebar-nav .dropdown-menu { background:#1a2940; padding:10px 0;}
.pmd-sidebar .pmd-sidebar-nav li.entity-drop a { background:#2C343F;}
.pmd-sidebar .pmd-sidebar-nav li .dropdown-menu li a { padding-left:65px; padding-top:10px; padding-bottom:10px; background-color: transparent;}
.pmd-sidebar .pmd-sidebar-nav li .dropdown-menu li a:hover, .pmd-sidebar .pmd-sidebar-nav li .dropdown-menu li a:focus, .pmd-sidebar .pmd-sidebar-nav li .dropdown-menu li a.active { opacity:1; color:#fff;}
.pmd-sidebar .pmd-sidebar-nav .dropdown-menu li a { padding-left:24px;  padding-top:15px; padding-bottom:15px;}
.pmd-sidebar .dropdown-menu > li > a:hover, .pmd-sidebar .dropdown-menu > li > a:focus, .pmd-sidebar .dropdown-menu > li > a.active { color:#ffffff;}
.pmd-content { margin-left: 64px;}
.pmd-sidebar { width:64px; min-width:64px; transform:translate3d(0px, 0px, 0px); -webkit-transform: translate3d(0, 0, 0); -moz-transform: translate3d(0, 0, 0); background-color:#1f2f46;}
.pmd-sidebar.pmd-sidebar-open {min-width: 274px; width: 274px;}
.pmd-sidebar .pmd-sidebar-nav li a { padding: 18px 20px 16px; color:#c8c8c8; background-color:transparent;}
.pmd-sidebar-left .nav > li > a:hover svg.stroke-fill path, .pmd-sidebar-left .nav > li > a:focus svg.stroke-fill path, .pmd-sidebar-left .nav > li > a:active svg.stroke-fill path, .pmd-sidebar-left .nav > li > a.active svg.stroke-fill path {stroke: #fff; fill: none;}
.pmd-sidebar-left .nav > li > a:hover svg circle, .pmd-sidebar-left .nav > li > a:focus svg circle, .pmd-sidebar-left .nav > li > a:active svg circle, .pmd-sidebar-left .nav > li > a.active svg circle {fill: #fff;}
/*online order icon*/
.pmd-sidebar-left .nav > li > a:hover svg.stroke-fill.online-order path, .pmd-sidebar-left .nav > li > a:focus svg.stroke-fill.online-order path, .pmd-sidebar-left .nav > li > a:active svg.stroke-fill.online-order path, .pmd-sidebar-left .nav > li > a.active svg.stroke-fill.online-order path {stroke: #fff;}
.pmd-sidebar-left .nav > li > a:hover svg.stroke-fill.online-order polygon, .pmd-sidebar-left .nav > li > a:focus svg.stroke-fill.online-order polygon, .pmd-sidebar-left .nav > li > a:active svg.stroke-fill.online-order polygon, .pmd-sidebar-left .nav > li > a.active svg.stroke-fill.online-order polygon {fill: #fff;}
/*reports icon*/
.pmd-sidebar-left .nav > li > a:hover svg.reports rect, .pmd-sidebar-left .nav > li > a:focus svg.reports rect, .pmd-sidebar-left .nav > li > a:active svg.reports rect, .pmd-sidebar-left .nav > li > a.active svg.reports rect {fill: #fff;}

/*svg*/
.stake-chart .set-svg svg { max-height:37px;}
.set-svg svg {max-height: 30px;}
.pmd-display2 { font-size:28px;}
.sidebar-with-icons .material-icons.media-left{padding-right: 16px;}
@media (max-width: 1200px) {
.pmd-sidebar:hover{width: 274px; max-width:274px;}
.highcharts-container { width: 100% !important;}
}
@media (min-width: 768px) {
.pmd-sidebar-overlay.pmd-sidebar-overlay-active{visibility: hidden; opacity: 0;}
}
@media (max-width: 767px) {
.pmd-sidebar { transform:translate3d(-280px, 0px, 0px);}
.pmd-content{margin-left:0;}
}
/*====================section custom css================*/
.section-custom{ max-width:800px;}

/*========================= Inbox Style starts =========================*/
.mailbox .tab-pane h5{font-size:14px;color:rgba(0,0,0,0.40);margin:30px 0 10px;font-weight:400;text-align:right;}
.mailbox .avtar-detail{color:#333;display:table-cell;}
.mailbox .user-details{min-width:200px;}
.mailbox .user-details .avatar-list-img40x40{float:left;margin-right:16px;display:table-cell;}
.mailbox .date-time{font-size:12px;font-weight:500;color:rgba(0,0,0,0.44);display:inline-block;text-align:right;min-width:80px;line-height:1.25;}
.mailbox .list-group-item{ padding:16px; border-bottom:solid 1px #eee;}
.mailbox .list-group-item .message-detail .list-group-item-heading{line-height:18px;}
.mailbox .read.list-group-item{background:#f9f9f9;}
.mailbox .list-group-item .message-detail .list-group-item-heading span{color:rgba(0,0,0,0.54);font-size:14px;font-weight:400;} 
.mailbox .list-group-item .avtar-detail .list-group-item-heading span{color:rgba(0,0,0,0.54);font-size:15px;}
.mailbox .list-group-item .avtar-detail .list-group-item-text{font-size:13px;line-height:1.25;margin-top:4px;display:inline-block;color:rgba(0, 0, 0, 0.54);opacity:1;}
.mailbox .action-bar .checkbox{margin:4px 0 0 16px; width:18px;}
.mailbox .action-bar .checkbox .custom-checkbox{width:18px;height:16px;display:inline-block;vertical-align:middle;}
.mailbox .action-bar .btn-delete{background:transparent;padding:5px;height:35px;width:auto;min-width:inherit;vertical-align:middle;line-height:31px; margin-left:10px;}
.mailbox .action-bar .floatinglabels{position:relative;}
.mailbox .action-bar .floatinglabels .form-control{border-color:#ccc;}
.mailbox .action-bar .dic-search{bottom: 5px;color: #848484;display: inline-block;font-size: 20px;position: absolute;right: 0;width: 20px;}
.mailbox .action-bar .btn-delete:hover .dc{background-position:center 0px;}
.mailbox .action-bar .btn-delete .dc{vertical-align:middle;margin:0;}
.mailbox .media-left.media-check{vertical-align:middle;padding-right:4px;}
.btn-delete .dic{width:22px;height:26px;display:inline-block;vertical-align:middle;color:rgba(0,0,0,0.54)}
.btn-delete:hover .dic{color:rgba(0,0,0,0.84)}
.mailbox .checkbox label.pmd-checkbox{ padding-right:0;}
.mailbox .list-group-item a:hover, .mailbox .list-group-item a:focus, .mailbox .list-group-item a:active{ text-decoration:none;}
@media (max-width: 767px) {
.mailbox .use-details { padding: 0; width: 100%;}
.mailbox .media-body { display: block; margin-top: 8px; padding-left: 30px; width: auto;}
.mailbox .date-time{min-width:inherit;  padding-left: 19px;}
.mailbox .user-details{min-width:100%;}
.mailbox .action-bar .btn-delete{ padding:5px 2px;}
}

/*========================= Notification  Start=========================*/
.btn-group .list-group .list-group-item-text{font-size:13px;line-height:20px;display:inline-block;font-weight:400;color:#666;font-family:Roboto}
.btn-group .list-group .list-group-item-date,.page-content .list-group .list-group-item-date{display:block;color:#a4a4a4;font-size:13px}
.btn-group .list-group .list-group-item a{font-weight:500;color:#333;font-family:Roboto}
.btn-group .list-group li:last-child { border:0;}
.notifications .media-body{overflow:visible}
.page-content > h2{color:#a4a4a4}
.page-content .list-group-item-text{font-family:roboto;font-weight:300; opacity:1; font-size:16px}
.page-content .list-group-item-text a{font-weight:500; color:#333;}
.notifications .card .list-group-item{border-bottom:1px solid #e8e8e8;border-radius:0;margin:0;position:relative;padding:15px}
.page-content .list-group-item.last{border:none;padding-bottom:0}
.list-group-item.saved-article:hover,.list-group-item.saved-article.unread, .list-group-item.unread{background:#F7F7F7}
.list-group-item.saved-article.unread, .list-group-item.unread{ background:#fff;}
.list-group-item-text {opacity: 0.74;}
.notifications .list-group-item.new-day:first-child{margin-top:46px}
.notifications .list-group-item.new-day::before{content:attr(data-date);display:block;font-size:13px;font-weight:500;left:0;position:absolute;color:rgba(51,51,51,0.64);top:-30px;}
.notifications .list-group-item.new-day{border-top:1px solid rgba(0,0,0,0.08);margin-top:62px}
.mobile-only { display:none !important;}

/*=================Login css====================*/
.body-custom{ height:100%;}
.logincard{margin:auto; position:relative; top:50%; max-width:360px; transform: translate(0px, -50%);}
.logincard .checkbox{ margin-left:0px;}
.logincard .checkbox label{padding:0;}
.logincard .dic{ margin-right:7px;}
.logincard .card-header{ padding:0; margin:0px auto;}
.loginlogo{ text-align:center; padding:30px 15px 10px;}
.loginlogo a img{ max-width:100%;}
.forgot-password{padding-top:8px;}
.remember{ color:#999;}
.redirection-link{ margin-top:30px; margin-bottom:30px;}
.card-footer-no-border{ border-color:transparent; padding-top:0;}
.card-footer-p16{ padding-left:20px; padding-right:20px; }
.login-card h3,
.register-card h3,
.forgot-password-card h3{ color:rgba(0, 0, 0, 0.87); font-size:16px; margin-top:0; margin-bottom:20px;}
.login-card .card-body,
.register-card .card-body{ padding-bottom:10px; margin:0;}
.register-card, .forgot-password-card{ display:none;}
.register-card, .login-card, .forgot-password-card{ position:relative;}
.register-card h3 span,
.login-card h3 span{ color:rgba(0, 0, 0, 0.54);}
.forgot-password-card h3 span{ font-size:14px; color:rgba(0, 0, 0, 0.54); width:80%; margin-top:5px; display:inline-block; line-height:18px;}
.logincard .alert{ display:none;}
.forgot-password-card .form-group.pmd-textfield{ margin-bottom:55px;}
.card-footer .pmd-checkbox-label {display: inline-block;margin-top: -2px;}
@media screen and (max-width: 768px) {
.logincard{max-width:300px;}	
.forgot-password a{font-size: 0.7rem;}
}
@media screen and (max-width: 480px) {
.logincard .loginlogo img { height:60px;}
.logincard .card { margin-bottom:0;}
}
@media (min-width: 321px) and (max-width: 480px) {
.logincard { max-width: 330px;}
}
@media (max-width: 320px) {
.logincard { max-width: 290px;}	
}

/*==========================Contact Start============================*/
.contact-page {margin-bottom: 250px;position: relative;}
.contact-page .contact-form {bottom: 0;left: 0;margin-top: -436px;position: relative;right: 0;top: 200px;}
.contact-page .contact-details {display: table; width:100%;}
.contact-page .feedback-form {padding: 30px 20px;width: 100%;}
.contact-page .fill .adress-col {background: #4285f4 none repeat scroll 0 0;display: table-cell;float: none;padding-bottom: 30px;padding-top: 30px;vertical-align: top;}
.contact-page .pmd-display1 {color: #4d4d4d;margin-top: 0;opacity: 1; font:33px/40px "Roboto";}
.contact-page .fill .adress-col .contact-info {color: #fff;margin-top: 35px;}
.contact-page .fill .adress-col .contact-info span b {text-transform: uppercase; color:#fff; font-weight:300; opacity:0.74; font-size:13px;}
.contact-page .fill .adress-col .contact-info span a{ color:#fff; opacity:0.74;}
.contact-page .contact-details .content-title{ color:#fff;}
.contact-page .fill .adress-col .content-title {color: #fff;margin-top: 0; font:33px/40px "Roboto";}
.feedback-form .sub-title {color: #737373;font-size: 18px;font-weight: 300;line-height: 20px;margin-bottom: 20px;}
@media (max-width: 990px) {
.contact-page .contact-form{ padding:0 20px;}
.contact-page .fill .adress-col{ display:inline-block;}
}
@media (max-width: 767px) {
.contact-page .contact-form{ padding:0 8px;}
.contact-page .fill .adress-col .contact-info{ margin-top: 20px;}
}

/*=================== About css ==============================*/
.about-content{ padding:0 20px 16px;}
.about-content ol{ padding-left:16px;}
.about-content h3{ font-weight:bold;}

/*================== 404 page========================*/
.body-404page{ background-color:#1453a1; display:block;}
.errorpage{ height:100%;}
.wrapper {min-height: 100%;position: relative; background: url("themes/images/propeller-bg.jpg") bottom no-repeat;}
.header-primary {margin-top: 160px;padding: 8px 0;text-align: center;}
.header-primary > a {transition: opacity 0.3s ease-in-out 0s;}
.header-primary .logo {animation: 1.2s linear 0s normal none 1 running fadeInUp;}
.content-primary {margin: 0 auto;padding: 0px 0 224px;text-align: center;color:#fff;}
.content-primary .section-footer {margin-top: 40px;}
.content-primary h1.title{ color:#fff; font-size:36px;}
.content-primary > h1 {animation: 1.7s linear 0s normal none 1 running fadeInUp;margin-bottom: 20px;}
.content-primary > p{ animation:1.9s linear 0s normal none 1 running fadeInUp;}
.content-primary .section-footer .btn{ margin:0 8px; min-width:160px;}
.content-primary .section-footer .btn.btn-primary{ animation:2.4s linear 0s normal none 1 running fadeIn;}
.content-primary .section-footer .btn.btn-secondary{ animation:2.8s linear 0s normal none 1 running fadeIn;}

/*======================== Blank page======================*/
.no-table-found h2 {color:#555;}
.no-table-found p {margin-bottom: 20px; opacity: 0.75; color:#555;}
.no-table-found .btn{font-size: 15px; padding: 14px 30px; width: 290px;}

/*==================== todo list css ====================*/
.todos .pmd-card-list{ min-height:312px;}
.todo-lists .pmd-checkbox{ margin-bottom:0; vertical-align:middle;}
.todo-lists span{ display:table-cell;}

/*============= Profile css ======================*/
.fileinput { margin-bottom: 9px; display: inline-block;}
.fileinput .form-control {padding-top: 7px;padding-bottom: 5px;display: inline-block;margin-bottom: 0px;vertical-align: middle;cursor: text;}
.fileinput .thumbnail {overflow: hidden;display: inline-block;margin-bottom: 5px;vertical-align: middle;text-align: center; width:100%; min-height:50px; background-color:#f8f8f8}
.fileinput .thumbnail > img {max-height: 100%;}
.fileinput .btn {vertical-align: middle;}

.fileinput .btn.btn-default, 
.fileinput .btn.btn-default, 
.fileinput .btn.btn-default{ background-color:rgba(0, 0, 0, 0.2);}

.fileinput-exists .fileinput-new,.fileinput-new .fileinput-exists {  display: none;}
.fileinput-inline .fileinput-controls {  display: inline;}
.fileinput-filename {  vertical-align: middle;  display: inline-block;  overflow: hidden;}
.form-control .fileinput-filename {  vertical-align: bottom;}
.fileinput.input-group {  display: table;}
.fileinput.input-group > * {  position: relative;  z-index: 2;}
.fileinput.input-group > .btn-file {  z-index: 1;}
.fileinput-new.input-group .btn-file,.fileinput-new .input-group .btn-file {  border-radius: 0 4px 4px 0;}
.fileinput-new.input-group .btn-file.btn-xs,.fileinput-new .input-group .btn-file.btn-xs,.fileinput-new.input-group .btn-file.btn-sm,.fileinput-new .input-group .btn-file.btn-sm {  border-radius: 0 3px 3px 0;}
.fileinput-new.input-group .btn-file.btn-lg,.fileinput-new .input-group .btn-file.btn-lg {  border-radius: 0 6px 6px 0;}
.form-group.has-warning .fileinput .fileinput-preview {  color: #8a6d3b;}
.form-group.has-warning .fileinput .thumbnail {  border-color: #faebcc;}
.form-group.has-error .fileinput .fileinput-preview {  color: #a94442;}
.form-group.has-error .fileinput .thumbnail {  border-color: #ebccd1;}
.form-group.has-success .fileinput .fileinput-preview {  color: #3c763d;}
.form-group.has-success .fileinput .thumbnail {  border-color: #d6e9c6;}
.input-group-addon:not(:first-child) {  border-left: 0;}
.btn.btn-file{overflow:hidden;position:relative;vertical-align:middle; min-width:36px; padding:5px 5px 4px;}
.btn.btn-file .dic{font-size:20px;}
.btn.btn-file > input[type=file]{width:100%;position:absolute;left:0;top:0;opacity:0;cursor:pointer}
.fileupload .uneditable-input{display:inline-block;margin-bottom:0;vertical-align:middle;height:28px !important}
.fileupload .thumbnail{overflow:hidden;display:inline-block;margin-bottom:10px;vertical-align:middle;text-align:center}
.fileupload .thumbnail > img{display:inline-block;vertical-align:middle;max-height:100%}
.fileupload .btn{vertical-align:middle}
.fileupload-exists .fileupload-new,.fileupload-new .fileupload-exists{display:none}
.fileupload-inline .fileupload-controls{display:inline}
.fileupload-new .input-append .btn-file{-webkit-border-radius:0 3px 3px 0;-moz-border-radius:0 3px 3px 0;border-radius:0 3px 3px 0}
.fileupload .fileupload-preview {vertical-align:middle}
.fileupload .close.fileupload-exists {vertical-align:middle}
.btn-file .ripple-wrapper{ display:none;}
.action-button{opacity:0;position:absolute; bottom:15px; left:0px; text-align:center; width:100%;-webkit-transition: 0.4s ease-in-out;-moz-transition: 0.4s ease-in-out;-o-transition: 0.4s ease-in-out;transition: 0.4s ease-in-out;}
.fileinput{width:166px;-webkit-transition: 0.4s ease-in-out;overflow:hidden;-moz-transition: 0.4s ease-in-out;-o-transition: 0.4s ease-in-out;transition: 0.4s ease-in-out; margin-right:10px;}
.fileinput:hover .action-button{opacity:1;}
.fileupload .btn{ padding:5px 10px; min-width:36px; background-color:#FFF !important; font-size:15px;}
.prousername{margin-bottom:20px;}
.profile-edit .pmd-textfield .form-control-static{ padding-top:4px;}

.pmd-card{ margin-bottom:0;}
.component-section{ margin-bottom: 20px; margin-top: 30px;}
.component-section h2{ margin-top:0;}
.component-box{ margin-bottom: 20px;}
.component-desc{ font-size:14px; margin-top:4px;}
@media (max-width: 767px) {
.component-section{ margin-top:0;}
}
.table-title-top-action{ margin-top:20px;}
.table-title-top-action a.btn{ margin-left:20px;}
.pmd-table-row-action{width:156px;}
.pmd-table-row-action a{ margin:-8px 0;}
.direct-expand i::before{ content: "\e148"; font-family: "Material icons";}
.direct-expand.child-table-collapse i::before{content: "\e15D";}
.login-card-section{ max-width:400px;}

.shadow-demo{ margin-bottom:30px; min-height:100px;}
.component-box .pmd-card-body > .btn{ margin: 5px 2px;}
.floating-action-custom .pmd-floating-action{ position:relative;}

.range-slider-container .pmd-card{ min-height:90px}

/*======================== Blank page======================*/
@media (max-width: 639px) {
h1.pmd-display4 { font-size: 4rem;}
}
/*======================== Modal page======================*/
@media (max-width: 768px) {
.component-box .pmd-card-body.modal-btn  .btn, .pmd-card-body.dropdown-btn .btn {margin: 5px 2px;}
}
/*======================== Tab page======================*/
.dropdown-tab .pmd-tabs .nav .open > a, .dropdown-tab .pmd-tabs .nav .open > a:hover, .dropdown-tab .pmd-tabs .nav .open > a:focus { background-color: transparent; border-color: transparent; opacity: 1;}
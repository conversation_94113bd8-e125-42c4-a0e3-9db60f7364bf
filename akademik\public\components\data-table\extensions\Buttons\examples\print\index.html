<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>

	<title>Buttons examples - Print</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Buttons example <span>Print</span></h1>

			<div class="info">
				<p>This set of examples shows how the <a href="//datatables.net/reference/button/print"><code class="button" title="Buttons button type">print</code></a> button
				type can be used and customised to display a printable version of the DataTable.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Print</a></h3>
						<ul class="toc">
							<li><a href="./simple.html">Print button</a></li>
							<li><a href="./message.html">Custom message</a></li>
							<li><a href="./columns.html">Export options - column selector</a></li>
							<li><a href="./select.html">Export options - row selector</a></li>
							<li><a href="./autoPrint.html">Disable auto print</a></li>
							<li><a href="./customisation.html">Customisation of the print view window</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
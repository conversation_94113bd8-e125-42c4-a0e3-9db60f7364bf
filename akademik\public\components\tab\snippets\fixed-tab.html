<!--Fixed tab example -->
<div class="pmd-card pmd-z-depth">
  <div class="pmd-tabs pmd-tabs-bg">
	  <div class="pmd-tab-active-bar"></div>
	  <ul role="tablist" class="nav nav-tabs nav-justified">
		<li class="active" role="presentation"><a data-toggle="tab" role="tab" aria-controls="home" href="#home-fixed">Default</a></li>
		<li role="presentation"><a data-toggle="tab" role="tab" aria-controls="profile" href="#about-fixed">Fixed</a></li>
		<li role="presentation"><a data-toggle="tab" role="tab" aria-controls="messages" href="#work-fixed">Scrollable</a></li>
	  </ul>
  </div>
  <div class="pmd-card-body">
	  <div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home-fixed">A tab provides the affordance for displaying grouped content. A tab label succinctly describes the tab’s associated grouping of content.</div>
		<div role="tabpanel" class="tab-pane" id="about-fixed">Fixed tabs have equal width, calculated either as the view width divided by the number of tabs, or based on the widest tab label. To navigate between fixed tabs, touch the tab or swipe the content area left or right.</div>
		<div role="tabpanel" class="tab-pane" id="work-fixed">To navigate between scrollable tabs, touch the tab or swipe the content area left or right. To scroll the tabs without navigating, swipe the tabs left or right.</div>
	  </div>
  </div>
</div> <!--Fixed tab example end-->
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="//code.jquery.com/jquery-1.11.3.min.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>AutoFill examples - Examples</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>AutoFill example <span>Examples</span></h1>

			<div class="info">
				<p>Spreadsheets such as Excel and Google Docs have a very handy data duplication option of an auto fill tool. The AutoFill library for DataTables provides a
				similar interface for DataTables (even extending upon it to provide complex data interaction options). AutoFill also provides full support for <a href=
				"https://editor.datatables.net">Editor</a> allowing end users to update data very quickly.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./initialisation/index.html">Initialisation</a></h3>
						<ul class="toc">
							<li><a href="./initialisation/simple.html">Basic initialisation</a></li>
							<li><a href="./initialisation/fills.html">Fill types</a></li>
							<li><a href="./initialisation/keyTable.html">KeyTable integration</a></li>
							<li><a href="./initialisation/events.html">Events</a></li>
							<li><a href="./initialisation/alwaysAsk.html">Always confirm action</a></li>
							<li><a href="./initialisation/columns.html">Column selector</a></li>
							<li><a href="./initialisation/focus.html">Click focus</a></li>
							<li><a href="./initialisation/scrolling.html">Scrolling DataTable</a></li>
							<li><a href="./initialisation/plugins.html">Fill plug-ins</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="./styling/bootstrap.html">Bootstrap styling</a></li>
							<li><a href="./styling/foundation.html">Foundation styling</a></li>
							<li><a href="./styling/jqueryui.html">jQuery UI styling</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
/*!
 * Propeller v1.0.0 (http://propeller.in)
 * Copyright 2016-2017 Digicorp, Inc.
 * Licensed under MIT (http://propeller.in/LICENSE)
 */

.pmd-tooltip ~ .tooltip { filter: alpha(opacity=0); opacity: 0; transition: opacity 0.5s ease-in-out, margin ease-in-out 0.3s; -moz-transition: opacity 0.3s ease-in-out, margin ease-in-out 0.3s; -ms-transition: opacity 0.5s ease-in-out, margin ease-in-out 0.3s; -o-transition: opacity 0.3s ease-in-out, margin ease-in-out 0.3s; -webkit-transition: opacity 0.3s ease-in-out, margin ease-in-out 0.3s; border-radius:2px; -moz-border-radius:2px; -ms-border-radius:2px; -o-border-radius:2px; -webkit-border-radius:2px;}

.pmd-tooltip ~ .tooltip .tooltip-arrow { display:none;}

.pmd-tooltip ~ .tooltip .tooltip-inner {background-color:transparent; padding:4px 8px; color: #fff; text-align: center;  text-decoration: none; font-size: 14px; font-weight: 500;line-height: 1.4;}

.pmd-tooltip ~ .tooltip:before { background-color: #323232; width: 0; height: 0; opacity: 1; position: absolute;  z-index: -1; content:""; 	left:50%; transform:scale(0); -moz-transform:scale(0); -ms-transform:scale(0); -o-transform:scale(0); -webkit-transform:scale(0); transition:all ease-in-out 0.2s; -moz-transition:all ease-in-out 0.2s; -o-transition:all ease-in-out 0.2s;	-webkit-transition:all ease-in-out 0.2s; -ms-transition:all ease-in-out 0.2s;}

/* tooltip Show*/
.pmd-tooltip ~ .tooltip.in {filter: alpha(opacity=100); opacity:100; }
.pmd-tooltip ~ .tooltip.in:before { width:100%; height:100%; left:0; opacity: 1; transform:scale(1); -moz-transform:scale(1); -ms-transform:scale(1); -o-transform:scale(1); -webkit-transform:scale(1);}

/* tooltip top*/
.pmd-tooltip ~ .tooltip.top:before {top:100%;}
.pmd-tooltip ~ .tooltip.in.top { margin-top:-10px;}
.pmd-tooltip ~ .tooltip.in.top:before { top:0; transform-origin:50% 100% 0; -moz-transform-origin:50% 100% 0; -ms-transform-origin:50% 100% 0; -webkit-transform-origin:50% 100% 0; -o-transform-origin:50% 100% 0;}

/* tooltip bottom*/
.pmd-tooltip ~ .tooltip.bottom:before {top:0;}
.pmd-tooltip ~ .tooltip.in.bottom { margin-top:10px;}
.pmd-tooltip ~ .tooltip.in.bottom:before { transform-origin: 50% 0 0; -moz-transform-origin: 50% 0 0; -ms-transform-origin: 50% 0 0; -o-transform-origin: 50% 0 0; -webkit-transform-origin: 50% 0 0;}

/* tooltip right*/
.pmd-tooltip ~ .tooltip.right:before {top:50%; left:0;}
.pmd-tooltip ~ .tooltip.right .tooltip-arrow{left: 0;}
.pmd-tooltip ~ .tooltip.in.right {margin-left:10px;}
.pmd-tooltip ~ .tooltip.in.right:before {top:0; transform-origin: 0% 50% 0; -moz-transform-origin: 0% 50% 0; -ms-transform-origin: 0% 50% 0; -o-transform-origin: 0% 50% 0; -webkit-transform-origin: 0% 50% 0;}

/* tooltip left*/
.pmd-tooltip ~ .tooltip.left:before { top:50%; left:100%;}
.pmd-tooltip ~ .tooltip.left .tooltip-arrow{right:0;}
.pmd-tooltip ~ .tooltip.in.left .tooltip-arrow{right:0;}
.pmd-tooltip ~ .tooltip.in.left {margin-left:-10px;}
.pmd-tooltip ~ .tooltip.in.left:before { left:0; top:0; transform-origin: 100% 50% 0; -moz-transform-origin: 100% 50% 0; -ms-transform-origin: 100% 50% 0; -webkit-transform-origin: 100% 50% 0; -o-transform-origin: 100% 50% 0;}


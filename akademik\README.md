# Sistem Akademik - Setup Instructions

## Implementasi yang Telah Selesai

✅ **Menu <PERSON>** - Mengelola data pengguna berdasarkan struktur tabel pengguna
✅ **Menu Mahasiswa** - Mengelola data mahasiswa berdasarkan struktur tabel mahasiswa

## Fitur yang Tersedia

### Menu Pengguna
- Tambah pengguna baru
- Edit pengguna existing
- Hapus pengguna
- Validasi username unik
- Hash password otomatis
- Level akses: Administrator, <PERSON><PERSON>, <PERSON>hasiswa

### Menu Mahasiswa  
- Tambah mahasiswa baru
- Edit mahasiswa existing
- Hapus mahasiswa
- Validasi NIM unik
- Dropdown jurusan dengan nama fakultas
- Input tanggal lahir dengan date picker

## Setup Database

1. Import file `akademik.sql` ke database MySQL
2. Jalankan query berikut untuk menambah data sample:

```sql
-- Menambah 1 pengguna tambahan (total 3 pengguna)
INSERT INTO `pengguna` (`username`, `password`, `nama`, `level_akses`) VALUES
('student1', '$2y$13$HLg8jF4J3XBh1I48CjXkI.B5f7PuT9cZvvO3uIaEmR.VePrdstIrW', 'Ahmad Mahasiswa', 'Mahasiswa');

-- Menambah 1 mahasiswa (data saya sendiri)
INSERT INTO `mahasiswa` (`nim`, `nama`, `tempat_lahir`, `tanggal_lahir`, `jenis_kelamin`, `jurusan_id`, `tahun_masuk`, `foto`) VALUES
('2024001001', 'Augment Assistant', 'Jakarta', '2000-01-01', 'Laki-laki', 1, 2024, 'default.jpg');
```

## Konfigurasi

Update file `akademik/includes/config.php` sesuai environment Anda:

```php
$config = array(
    "site"      => "http://localhost/akademik",
    "server"    => "path/to/your/akademik/folder",
    "driver"    => "mysql", 
    "database"  => "akademik",
    "user"      => "root",
    "password"  => "",
    "host"      => "localhost",
    "port"      => 3306,
    "token"     => "test"
);
```

## Akses Aplikasi

1. **Login Admin**: 
   - Username: `admin`
   - Password: `admin123`

2. **Login Dosen**:
   - Username: `mfikry` 
   - Password: `admin123`

3. **Login Mahasiswa**:
   - Username: `student1`
   - Password: `password123`

## URL Akses

- **Login**: `http://localhost/akademik/`
- **Admin Panel**: `http://localhost/akademik/admin`
- **Menu Pengguna**: `http://localhost/akademik/admin/Pengguna`
- **Menu Mahasiswa**: `http://localhost/akademik/admin/Mahasiswa`

## Data yang Telah Diisi

### Pengguna (3 total):
1. admin - Administrator
2. mfikry - Muhammad Fikry (Dosen)  
3. student1 - Ahmad Mahasiswa (Mahasiswa)

### Mahasiswa (1 total):
1. NIM: 2024001001 - Augment Assistant

## Struktur File

```
akademik/
├── components/
│   ├── pengguna.php (✅ Updated with full CRUD)
│   ├── mahasiswa.php (✅ New component with full CRUD)
│   ├── fakultas.php
│   ├── jurusan.php
│   └── ...
├── includes/
│   ├── config.php (✅ Updated)
│   ├── class.php
│   └── ...
├── webpages/
│   ├── administrator.php (✅ Menu sudah ada)
│   └── ...
└── ...
```

## Catatan

- Password default untuk semua user adalah `admin123` atau `password123`
- Semua password di-hash menggunakan PHP `password_hash()`
- Menu Pengguna dan Mahasiswa sudah tersedia di sidebar admin
- Validasi form lengkap dengan pesan error
- Responsive design menggunakan Propeller CSS framework

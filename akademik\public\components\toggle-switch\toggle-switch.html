<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="Propeller is a front-end responsive framework based on Material design & Bootstrap.">
	<meta content="width=device-width, initial-scale=1, user-scalable=no" name="viewport">
    <title>Form - Propeller Components</title>

	<!-- favicon --> 	
	<link href="http://propeller.in/assets/landing-page/images/favicon.ico" type="image/x-icon" rel="icon"  />
    
   	<!-- Bootstrap --> 
	<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" type="text/css" rel="stylesheet" /> 
 
    <!-- Propeller card (CSS for helping component example file)-->
	<link href="http://propeller.in/components/card/css/card.css" type="text/css" rel="stylesheet" />
	
	<!-- Example docs (CSS for helping component example file)-->
	<link href="http://propeller.in/docs/css/example-docs.css" type="text/css" rel="stylesheet" />
	
	<!-- Propeller typography (CSS for helping component example file) -->
	<link href="http://propeller.in/components/typography/css/typography.css" type="text/css" rel="stylesheet" />

	<!-- Google Icon Font -->
    <link href="http://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
	<link href="http://propeller.in/components/icons/css/google-icons.css" type="text/css" rel="stylesheet" />
	
	<!-- Propeller Checkbox -->
    <link href="http://propeller.in/components/checkbox/css/checkbox.css" type="text/css" rel="stylesheet" />

	<!-- Propeller textfield -->
    <link href="http://propeller.in/components/textfield/css/textfield.css" type="text/css" rel="stylesheet" />
    
	<!-- Propeller Radio -->
    <link href="http://propeller.in/components/radio/css/radio.css" type="text/css" rel="stylesheet" />
    
	<!-- Propeller Toggle -->
    <link href="http://propeller.in/components/toggle-switch/css/toggle-switch.css" type="text/css" rel="stylesheet" />

</head>
<body>

<!--Form-->
<div class="pmd-content pmd-content-custom" id="content"> 
	
	<!--component header -->
	<div class="componant-title-bg"> 
		<div class="container">
			<div class="row">
			
				<!-- component title and description-->
				<div class="col-xs-12">
					<h1>Form</h1>
					<p class="lead">An HTML form is a section of a document containing normal content, markup, special elements called controls (checkboxes, radio buttons, menus, etc.), and labels on those controls. Users generally "complete" a form by modifying its controls (entering text, selecting menu items, etc.), before submitting the form to an agent for processing (e.g., to a Web server, to a mail server, etc.)</p>
				</div><!-- component title and description end--> 
				
			</div>
		</div>
	</div><!--end component header-->

	<div class="container">
		<!-- Toggle Switch -->
		<section class="row component-section">
			
			<!-- toogle switch title and description -->
			<div class="col-md-3">
				<div id="toggle">
					<h2>Toggle Switch</h2>
				</div>
				<p>Toggle Switch is - To switch from one setting to another. It implies that there are only two possible settings and that you are switching from the current setting to the other setting.
                For example, light switches that turn a light on or off are toggle switches.</p>
				<p>Add <code>.pmd-switch</code> to the parent div and <code>.pmd-switch-label</code> to the tag containing label.</p>
			</div> <!-- toogle switch title and description end -->
			
			<!-- toggle switch code and example -->
			<div class="col-md-9">
				<div class="component-box">
					<!-- toggle switch example -->
					<div class="row toggle-button-custom">
						<div class="col-md-6">
							<div class="pmd-card pmd-z-depth pmd-card-custom-form">
								<div class="pmd-card-body">
									<div class="pmd-switch">
										<label>
											<input type="checkbox">
											<span class="pmd-switch-label"></span> </label>
									</div>
									<div class="pmd-switch">
										<label>
											<input type="checkbox">
											<span class="pmd-switch-label"></span> </label>
									</div>
								</div>
							</div>
							<p class="component-desc">Light theme</p>
						</div>
						<div class="col-md-6">
							<div class="pmd-card pmd-card-inverse pmd-z-depth pmd-card-custom-form">
								<div class="pmd-card-body">
									<div class="pmd-switch">
										<label>
											<input type="checkbox">
											<span class="pmd-switch-label"></span> </label>
									</div>
									<div class="pmd-switch">
										<label>
											<input type="checkbox">
											<span class="pmd-switch-label"></span> </label>
									</div>
								</div>
							</div>
							<p class="component-desc">Dark theme</p>
						</div>
					</div> <!-- toggle switch example end -->
						
				</div>
			</div> <!-- toggle switch code and example end -->
		</section> <!-- Toggle Switch end -->		 

	</div> <!--container end --> 
	
</div> <!--Form-->

</body>
<!-- Jquery js -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>

<!-- Propeller checkbox js -->
<script type="text/javascript" src="http://propeller.in/components/checkbox/js/checkbox.js"></script>

<!-- Propeller checkbox js -->
<script type="text/javascript" src="http://propeller.in/components/textfield/js/textfield.js"></script>

<!-- Propeller checkbox js -->
<script type="text/javascript" src="http://propeller.in/components/radio/js/radio.js"></script>

</html>

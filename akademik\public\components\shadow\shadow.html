<!doctype html>
<html lang="">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="Propeller is a front-end responsive framework based on Material design & Bootstrap.">
	<meta content="width=device-width, initial-scale=1, user-scalable=no" name="viewport">
    <title>Shadow - Style - Propeller</title>
    
	<!-- favicon --> 
	<link rel="icon" href="http://propeller.in/assets/images/favicon.ico" type="image/x-icon">
	
	<!-- Bootstrap --> 
	<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" type="text/css" rel="stylesheet" /> 
	
	<!-- Propeller typography (CSS for helping component example file)-->
	<link href="http://propeller.in/components/typography/css/typography.css" type="text/css" rel="stylesheet" />
	
	<!-- Example docs (CSS for helping component example file)-->
	<link href="http://propeller.in/docs/css/example-docs.css" type="text/css" rel="stylesheet" />

	<!-- Propeller Shadow -->
	<link href="css/shadow.css" type="text/css" rel="stylesheet"/> 

</head>


<body>
	<!-- Shadow -->
	<div class="pmd-content pmd-content-custom" id="content">
	
		<!-- component header -->
		<div class="componant-title-bg"> 
			<div class="container">
				<div class="row">
					<!-- component title and description -->
					<div class="col-xs-12">
						<h1>Shadow</h1>
						<p class="lead">In material design, everything should have a certain <code>pmd-z-depth</code> that determines how far raised or close to the page the element is.</p>
					</div> <!-- component title and description end -->
					
				</div>
			</div>
		</div> <!-- component header end-->
		
		<div class="container">
			
			<!-- Shadow types -->
			<section class="row component-section">
				
				<!-- shadow types title and description -->
				<div class="col-md-3">
					<div id="types">
						<h2>Types</h2>
					</div>
					<p>You can easily apply this shadow effect by adding a <code>.pmd-z-depth</code> to an HTML tag. Alternatively you can extend any of these shadows using <code>.pmd-z-depth-1</code>, <code>.pmd-z-depth-2</code> and more.</p>
				</div> <!-- shadow types title and description end-->
				
				<!--Shadow type code and example -->
				<div class="col-md-9">
					<div class="component-box">
						<!-- shadow examples -->
						<div class="row">
							<div class="col-md-12"> 
								<!-- Shadow varients -->
								<div class="pmd-card-body">
									<div class="row">
										<div class="col-lg-4 col-md-4 col-xs-6"> 
											<!--pmd-z-depth-->
											<p class="pmd-z-depth shadow-demo">pmd-z-depth</p>
										</div>
										<div class="col-lg-4 col-md-4 col-xs-6"> 
											<!--pmd-z-depth-1-->
											<p class="pmd-z-depth-1 shadow-demo">pmd-z-depth-1</p>
										</div>
										<div class="col-lg-4 col-md-4 col-xs-6"> 
											<!--pmd-z-depth-2-->
											<p class="pmd-z-depth-2 shadow-demo">pmd-z-depth-2</p>
										</div>
										<div class="col-lg-4 col-md-4 col-xs-6"> 
											<!--pmd-z-depth-3-->
											<p class="pmd-z-depth-3 shadow-demo">pmd-z-depth-3</p>
										</div>
										<div class="col-lg-4 col-md-4 col-xs-6"> 
											<!--pmd-z-depth-4-->
											<p class="pmd-z-depth-4 shadow-demo">pmd-z-depth-4</p>
										</div>
										<div class="col-lg-4 col-md-4 col-xs-6"> 
											<!--pmd-z-depth-5-->
											<p class="pmd-z-depth-5 shadow-demo">pmd-z-depth-5</p>
										</div>
									</div>
								</div>
							</div>
						</div> <!-- shadow examples end -->
						
					</div>
				</div> <!--Shadow type code and example end -->
				
			</section> <!--Shadow types end -->
			 
		</div>
	</div>
	<!-- Shadow constructor end --> 
</body>
	

</html>

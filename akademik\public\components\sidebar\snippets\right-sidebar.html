<!-- Right Sidebar -->

<!-- Nav menu -->
<nav class="navbar navbar-inverse pmd-navbar navbar-fixed-top pmd-z-depth">
    <div class="container-fluid"> 
    
    <!-- Sidebar Toggle Button-->
    <!-- Brand and toggle get grouped for better mobile display -->
    <div class="navbar-header">
        <a class="navbar-brand" href="javascript:void(0);">Brand</a> 
    </div>
    <!-- Navbar Right icon -->		
    <div class="pmd-navbar-right-icon pull-right"> 
        <a href="javascript:void(0);" class="btn btn-sm pmd-btn-fab pmd-btn-flat pmd-ripple-effect btn-primary"><i class="material-icons pmd-sm">search</i></a>
        <a href="javascript:void(0);" class="btn btn-sm pmd-btn-fab pmd-btn-flat pmd-ripple-effect btn-primary topbar-toggle visible-xs-inline-block" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1"><i class="material-icons pmd-sm">more_vert</i></a>
        <a href="javascript:void(0);" class="btn btn-sm pmd-btn-fab pmd-btn-flat pmd-ripple-effect btn-primary pmd-sidebar-toggle-right"><i class="material-icons">more_horiz</i></a>   
    </div> 
    </div>
</nav>

<!-- Content -->    
<section id="pmd-main">
  <!-- Start Content -->
  <div class="pmd-content" id="content">
        <h2 class="headline">Sidebar Constructor</h2>
        <p>This structure shows a permanent app bar with a floating action button. The app bar absorbs elements from the tablet and mobile bottom bars.</p>
        <p style="margin-bottom:0;">An optional bottom bar can be added for additional functionality or action overflow. A side nav overlays all other structural elements. A right nav menu can be accessed temporarily or pinned for permanent display.<br><br></p>
  </div>

  <!-- Right sidebar -->
  <aside class="pmd-sidebar sidebar-custom sidebar-default pmd-sidebar-right-fixed pmd-z-depth" role="navigation" style="position: absolute;"> 
      <!-- Sidebar navigation -->
      <ul class="nav pmd-sidebar-nav">
          <li> <a class="pmd-ripple-effect" href="javascript:void(0);"> Trash</a> </li>
          <li> <a class="pmd-ripple-effect" href="javascript:void(0);"> Spam</a> </li>
          <li> <a class="pmd-ripple-effect" href="javascript:void(0);"> Follow Up</a> </li>
      </ul>
  </aside>
  <div class="pmd-sidebar-overlay"></div>	
</section>
/*!
 * Propeller v1.0.0 (http://propeller.in)
 * Copyright 2016-2017 Digicorp, Inc.
 * Licensed under MIT (http://propeller.in/LICENSE)
 */

/* 
---------------------------------------
	Propeller Style Guide Version 1.0.0
---------------------------------------	
* 	Primary Color : #001E80
* 	Secondary Color : #4acc8e
* 	Highlighted Text (Title) Color & Fonts : #333c4e, 'Roboto', sans-serif
* 	Body Text Color & Fonts : #333c4e, 'Roboto', sans-serif
font-family: 'Roboto', sans-serif;
font-family: 'Roboto', sans-serif;

---------------------------------------
	Elements Covered
---------------------------------------
* 	Typography
* 	Buttons
* 	Icons
* 	Form Elements and theme
--------------------------------------- */

@import url(https://fonts.googleapis.com/css?family=Roboto:400,700|Roboto:100,300,400,500,700);

html { height:100%;}
body {font-family: 'Roboto', sans-serif; font-size:16px; line-height:1.6; color:#4d575d; background-color:#f5f5f5;}

/*======= Typography =======*/

/* Font Size */
.pmd-display-4 {font-size:42px;line-height:1.4;}
.pmd-display-3 {font-size:28px;}
.pmd-display-2 {font-size:24px;}
.pmd-display-1 {font-size:20px;}
h5{font-size:16px;}

/* Custom Font Family */
.ff-roboto{font-family: 'Roboto', sans-serif !important;}
.ff-Roboto{font-family: 'Roboto', sans-serif !important;}
.fw-light{font-weight:300;}
.fw-regular{font-weight:400;}
.fw-medium{font-weight:500;}
.fw-bold{font-weight:700;}

/* Color & Weight style */
h1, h2, h3, h4, h5, h6{ color:#333c4e; font-family:'Roboto', sans-serif;}
h1 strong, h2 strong, h3 strong, h4 strong, h5 strong{font-weight:700;}

/* Other Element Style */
em{font-style:italic;font-family: 'Roboto', sans-serif;}
.lead {font-size:20px; color: rgba(51, 60, 78, 0.75); margin-bottom:25px; line-height:1.3}
strong, b{font-weight:700; color:#333c4e;}
label{font-weight:400;}
a:hover, a:focus{color:#4acc8e;}
a{ color:rgba(0, 0, 0, 0.84); text-decoration:none; outline:none;}
/*a:hover, a:focus, a:active{color:#00a9a6;text-decoration:underline;}*/

/*======= Input Field =======*/
.pmd-textfield .form-control{border-color:rgba(0,0,0,0.14);}
.pmd-textfield-focused{background-color:#001E80;}

/* Input Field & Selectbox Inverse Theme */
form.theme-inverse .pmd-textfield-focused, form.theme-inverse .pmd-textfield.has-error .form-control:invalid ~ .pmd-textfield-focused, form.theme-inverse .pmd-radio *:checked + span.pmd-radio-label::after{background-color: #ffffff;}
form.theme-inverse .pmd-textfield.has-error .form-control:invalid ~ .has-error-text, form.theme-inverse .pmd-textfield-floating-label label{color:rgba(255,255,255,0.54);}
form.theme-inverse .pmd-textfield.has-error .form-control:invalid{border-color:rgba(255,255,255,0.54); color:#fff;}
form.theme-inverse .has-error.pmd-textfield-floating-label.pmd-textfield-floating-label-completed label, form.theme-inverse .radio, form.theme-inverse .checkbox{color:#fff;}
form.theme-inverse .select2-container--default .select2-selection--single .select2-selection__rendered, form.theme-inverse .pmd-textfield .form-control{color:#fff;border-color:rgba(255,255,255,0.54)}

/*======= Error Input =======*/
.pmd-textfield.has-error .form-control:invalid{color:#e73e52;border-color:#e73e52}
.pmd-textfield.has-error .form-control:invalid ~ .pmd-textfield-focused{background-color:#e73e52;}
.has-error.pmd-textfield-floating-label.pmd-textfield-floating-label-completed label{color:#e73e52; opacity:0.64;}
.pmd-textfield.has-error .form-control:invalid ~ .has-error-text{font-size:12px;color:#e73e52;opacity:0.87;}

/*======= Background fill Color =======*/
.bg-fill-primary-color {background-color:#4285f4;} /*Propeller Primary color*/
.bg-fill-secondary-color {background-color:#4ACC8E;} /*Propeller Secondary color*/
.bg-fill-white {background-color:#ffffff;}
.bg-fill-darkgrey {background-color:#333333;}
.bg-fill-yellow { background-color:#ffc776;}
.bg-fill-darkblue {background-color:#333c4e;}

/*======= Typography fill Color =======*/
.typo-fill-darkblue {background-color:#333c4e;}
.typo-fill-darkblue-contrast{ background-color: rgba(51,60,78,0.87);}
.typo-fill-darkblue-contrast-light{ background-color: rgba(51,60,78,0.54);}

/*======= Button =======*/

/* Primary Button */
/*.btn-primary{background-color:#4acc8e;}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary:active:focus{background-color:#38B177; color:#fff;}*/

/* Secondary Button */
.btn-secondary{ background-color:#3c87da; color:#fff;}
.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active{background-color:#1e77d8; color:#fff;}

/* Outline Button */
.pmd-btn-outline{background-color:transparent;border:1px solid #4acc8e;color:#4acc8e;}
.pmd-btn-outline:hover, .pmd-btn-outline:focus, .pmd-btn-outline:active{background-color:#4acc8e; color:#fff;}

/* Default Button */
.btn-default{background-color:transparent;color:#333c4e;}
.btn-default:hover, .btn-default:focus, .btn-default:active{background-color:rgba(51,60,78,0.16); color:#333c4e;}

/* Button Ripple Background */
.btn .ink{background-color:rgba(255,255,255,0.74)}

/*======= Checkbox =======*/
.pmd-checkbox [type="checkbox"]:checked + .pmd-checkbox-label::before{ background-color:#4acc8e; border-color:#4acc8e;}
/* inverse */
.pmd-card-inverse .pmd-checkbox [type="checkbox"]:checked + .pmd-checkbox-label::before{background-color:#4acc8e; border-color:#4acc8e;}

/*======= Radio =======*/
.pmd-radio > span.pmd-radio-label::after{background-color:#4acc8e;}
.pmd-radio *:checked + span.pmd-radio-label::before{border-color:#4acc8e;}
/* inverse */
.pmd-card-inverse .pmd-radio > span.pmd-radio-label::after{background-color:#4acc8e;}
.pmd-card-inverse .pmd-radio *:checked + span.pmd-radio-label::before{ border-color:#4acc8e;}

/*======= Rounded Checkbox Theme =======*/
/* Normal */
.checkbox-rounded .pmd-checkbox.active span{color:#fff;font-weight:600;}
.checkbox-rounded .pmd-checkbox.active{background-color:#4acc8e}
.checkbox-rounded .pmd-checkbox [type="checkbox"]:not(:checked) + .pmd-checkbox-label::after, .checkbox-rounded .pmd-checkbox [type="checkbox"]:not(:checked) + .pmd-checkbox-label::after{color:#4acc8e;}
/* Checked */
.checkbox-rounded .pmd-checkbox [type="checkbox"]:checked + .pmd-checkbox-label::before{background-color:#fff;border-color:#fff;}
.checkbox-rounded .pmd-checkbox [type="checkbox"]:checked + .pmd-checkbox-label::after{color:#4acc8e;}

/* Inverse Theme */
/* Normal */
form.theme-inverse .checkbox-rounded .pmd-checkbox [type="checkbox"]:not(:checked) + .pmd-checkbox-label::before{border-color:rgba(0,0,0,0.34)}
form.theme-inverse .checkbox-rounded .pmd-checkbox [type="checkbox"]:not(:checked) + .pmd-checkbox-label::after, form.theme-inverse .checkbox-rounded .pmd-checkbox [type="checkbox"]:checked + .pmd-checkbox-label::after{color:#4acc8e;}
/* Checked */
form.theme-inverse .checkbox-rounded .pmd-checkbox [type="checkbox"]:checked + .pmd-checkbox-label::before{border-color:#fff;background-color:#fff;}

/*======= Background fill Color =======*/
.bg-fill-primary-color {background-color:#4285F4;} /*Propeller Primary color*/
.bg-fill-secondary-color {background-color: #51D184;} /*Propeller Secondary color*/
.bg-fill-darkblue {background-color: #1a2940;}
.bg-fill-lightblue {background-color: #1f2f46;}
.bg-fill-white {background-color:#ffffff;}
.bg-fill-orange { background-color:#f7912f;}
.bg-fill-violet { background-color:#04B5A3;}
.bg-fill-green { background-color:#3CAD45; }
.bg-fill-lightblue { background-color: #2f7af7;}
.bg-fill-feedback { background-color:#3c98d9;}
.bg-fill-sky { background-color:#65bfff;}
.bg-fill-red{ background-color:#EF3648;}

/*======= Navbar =======*/
.navbar-inverse .navbar-nav > li > a{ color:#fff;}
.navbar-inverse .navbar-brand{color:#fff;}
.pmd-user-info a{color:#fff;}
.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus,
.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus{ background-color:#2764c9;}
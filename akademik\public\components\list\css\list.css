/*!
 * Propeller v1.0.0 (http://propeller.in)
 * Copyright 2016-2017 Digicorp, Inc.
 * Licensed under MIT (http://propeller.in/LICENSE)
 */

/*Propeller*/
.pmd-card-list{ padding-bottom: 8px; padding-top: 8px; margin-bottom:0; background-color:#fff;}
.list-group-item { padding-top:16px; padding-bottom:18px; margin-bottom: -1px; border: inherit; line-height:1.4;}
.list-group-item-heading {margin-top: 0; margin-bottom: 0; display:block; line-height:1.4;}
.list-group-item-text {margin-bottom: 0; line-height:1.4; color:rgba(0,0,0,0.54); font-size:0.875rem;}
.list-group-item:first-child { border-top-left-radius: 0; border-top-right-radius: 0;}
.list-group-item:last-child { margin-bottom: 0; border-bottom-right-radius: 0; border-bottom-left-radius: 0;}

/*Textlist*/
.pmd-list .list-group-item{ padding-top:12px; padding-bottom:12px;}

/*Icon list*/
.pmd-list-icon .list-group-item{ padding-top:12px; padding-bottom:12px;}

/*Icon textlist twoline*/
.pmd-list-twoline .list-group-item{ padding-bottom:12px; padding-top:12px;}

/*Icon iconlist twoline*/
.pmd-list-icon .list-group-item{}

/*Icon list*/
.pmd-list-avatar{ padding:8px 0;}
.avatar-list-img{ border-radius:50%; width:40px; height:40px; overflow:hidden; display:inline-block; vertical-align:middle;}
.pmd-list-avatar .list-group-item{padding-top:8px; padding-bottom:8px;}

/*Media list*/
.material-icons.media-left{padding-right:32px; vertical-align:top; display:table-cell;}
.material-icons.media-right{padding-left:32px; vertical-align:top; display:table-cell;}
.material-icons.media-middle{ vertical-align:middle; display:table-cell;}
.media-left, .media > .pull-left {  padding-right: 16px;}
.media-body.pmd-word-break{ word-break: break-all; word-wrap: break-word;}

<!--Dropdown with header -->
<span class="dropdown pmd-dropdown clearfix">
    <button class="btn pmd-ripple-effect btn-primary dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-expanded="true">Dropdown with Header <span class="caret"></span></button>
    <ul aria-labelledby="dropdownMenu2" role="menu" class="dropdown-menu">
        <li class="dropdown-header" role="presentation">Dropdown header</li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Action</a></li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Another action</a></li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Something else here</a></li>
        <li class="dropdown-header" role="presentation">Dropdown header</li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Separated link</a></li>
    </ul>
</span>

<!--Dropdown with divider-->
<span class="dropdown pmd-dropdown clearfix">
    <button class="btn pmd-ripple-effect btn-primary dropdown-toggle" type="button" id="dropdownMenuDivider" data-toggle="dropdown" aria-expanded="true">Dropdown With Divider <span class="caret"></span></button>
    <ul aria-labelledby="dropdownMenuDivider" role="menu" class="dropdown-menu">
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Action</a></li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Another action</a></li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Something else here</a></li>
        <li class="divider" role="presentation"></li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Separated link</a></li>
    </ul>
</span>

<!--Dropdown with disable menu -->
<span class="dropdown pmd-dropdown clearfix">
    <button class="btn pmd-ripple-effect btn-primary dropdown-toggle" type="button" id="dropdownMenu3" data-toggle="dropdown" aria-expanded="true">Dropdown with disabled link <span class="caret"></span></button>
    <ul aria-labelledby="dropdownMenu3" role="menu" class="dropdown-menu">
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Regular link</a></li>
        <li class="disabled" role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Disabled link</a></li>
        <li role="presentation"><a href="javascript:void(0);" tabindex="-1" role="menuitem">Another link</a></li>
    </ul>
</span>

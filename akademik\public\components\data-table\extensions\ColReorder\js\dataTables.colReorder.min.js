/*!
 ColReorder 1.2.0
 ©2010-2014 SpryMedia Ltd - datatables.net/license
*/
(function(n,p,r){function o(d){for(var h=[],g=0,a=d.length;g<a;g++)h[d[g]]=g;return h}function m(d,h,g){h=d.splice(h,1)[0];d.splice(g,0,h)}function q(d,h,g){for(var a=[],c=0,e=d.childNodes.length;c<e;c++)1==d.childNodes[c].nodeType&&a.push(d.childNodes[c]);h=a[h];null!==g?d.insertBefore(h,a[g]):d.appendChild(h)}n=function(d,h){d.fn.dataTableExt.oApi.fnColReorder=function(a,c,e){var b,f,k,g,h=a.aoColumns.length,i,j;i=function(a,b,c){if(a[b]&&"function"!==typeof a[b]){var d=a[b].split("."),e=d.shift();
isNaN(1*e)||(a[b]=c[1*e]+"."+d.join("."))}};if(c!=e)if(0>c||c>=h)this.oApi._fnLog(a,1,"ColReorder 'from' index is out of bounds: "+c);else if(0>e||e>=h)this.oApi._fnLog(a,1,"ColReorder 'to' index is out of bounds: "+e);else{k=[];b=0;for(f=h;b<f;b++)k[b]=b;m(k,c,e);var l=o(k);b=0;for(f=a.aaSorting.length;b<f;b++)a.aaSorting[b][0]=l[a.aaSorting[b][0]];if(null!==a.aaSortingFixed){b=0;for(f=a.aaSortingFixed.length;b<f;b++)a.aaSortingFixed[b][0]=l[a.aaSortingFixed[b][0]]}b=0;for(f=h;b<f;b++){j=a.aoColumns[b];
k=0;for(g=j.aDataSort.length;k<g;k++)j.aDataSort[k]=l[j.aDataSort[k]];j.idx=l[j.idx]}d.each(a.aLastSort,function(b,c){a.aLastSort[b].src=l[c.src]});b=0;for(f=h;b<f;b++)j=a.aoColumns[b],"number"==typeof j.mData?(j.mData=l[j.mData],a.oApi._fnColumnOptions(a,b,{})):d.isPlainObject(j.mData)&&(i(j.mData,"_",l),i(j.mData,"filter",l),i(j.mData,"sort",l),i(j.mData,"type",l),a.oApi._fnColumnOptions(a,b,{}));if(a.aoColumns[c].bVisible){k=this.oApi._fnColumnIndexToVisible(a,c);g=null;for(b=e<c?e:e+1;null===
g&&b<h;)g=this.oApi._fnColumnIndexToVisible(a,b),b++;i=a.nTHead.getElementsByTagName("tr");b=0;for(f=i.length;b<f;b++)q(i[b],k,g);if(null!==a.nTFoot){i=a.nTFoot.getElementsByTagName("tr");b=0;for(f=i.length;b<f;b++)q(i[b],k,g)}b=0;for(f=a.aoData.length;b<f;b++)null!==a.aoData[b].nTr&&q(a.aoData[b].nTr,k,g)}m(a.aoColumns,c,e);m(a.aoPreSearchCols,c,e);b=0;for(f=a.aoData.length;b<f;b++)i=a.aoData[b],i.anCells&&m(i.anCells,c,e),"dom"!==i.src&&d.isArray(i._aData)&&m(i._aData,c,e);b=0;for(f=a.aoHeader.length;b<
f;b++)m(a.aoHeader[b],c,e);if(null!==a.aoFooter){b=0;for(f=a.aoFooter.length;b<f;b++)m(a.aoFooter[b],c,e)}(new d.fn.dataTable.Api(a)).rows().invalidate();b=0;for(f=h;b<f;b++)d(a.aoColumns[b].nTh).off("click.DT"),this.oApi._fnSortAttachListener(a,a.aoColumns[b].nTh,b);d(a.oInstance).trigger("column-reorder.dt",[a,{from:c,to:e,mapping:l,iFrom:c,iTo:e,aiInvertMapping:l}])}};var g=function(a,c){var e=(new d.fn.dataTable.Api(a)).settings()[0];if(e._colReorder)return e._colReorder;!0===c&&(c={});var b=
d.fn.dataTable.camelToHungarian;b&&(b(g.defaults,g.defaults,!0),b(g.defaults,c||{}));this.s={dt:null,init:d.extend(!0,{},g.defaults,c),fixed:0,fixedRight:0,reorderCallback:null,mouse:{startX:-1,startY:-1,offsetX:-1,offsetY:-1,target:-1,targetIndex:-1,fromIndex:-1},aoTargets:[]};this.dom={drag:null,pointer:null};this.s.dt=e;this.s.dt._colReorder=this;this._fnConstruct();return this};g.prototype={fnReset:function(){for(var a=[],c=0,d=this.s.dt.aoColumns.length;c<d;c++)a.push(this.s.dt.aoColumns[c]._ColReorder_iOrigCol);
this._fnOrderColumns(a);return this},fnGetCurrentOrder:function(){return this.fnOrder()},fnOrder:function(a){if(a===r){for(var a=[],c=0,d=this.s.dt.aoColumns.length;c<d;c++)a.push(this.s.dt.aoColumns[c]._ColReorder_iOrigCol);return a}this._fnOrderColumns(o(a));return this},_fnConstruct:function(){var a=this,c=this.s.dt.aoColumns.length,e=this.s.dt.nTable,b;this.s.init.iFixedColumns&&(this.s.fixed=this.s.init.iFixedColumns);this.s.init.iFixedColumnsLeft&&(this.s.fixed=this.s.init.iFixedColumnsLeft);
this.s.fixedRight=this.s.init.iFixedColumnsRight?this.s.init.iFixedColumnsRight:0;this.s.init.fnReorderCallback&&(this.s.reorderCallback=this.s.init.fnReorderCallback);for(b=0;b<c;b++)b>this.s.fixed-1&&b<c-this.s.fixedRight&&this._fnMouseListener(b,this.s.dt.aoColumns[b].nTh),this.s.dt.aoColumns[b]._ColReorder_iOrigCol=b;this.s.dt.oApi._fnCallbackReg(this.s.dt,"aoStateSaveParams",function(b,c){a._fnStateSave.call(a,c)},"ColReorder_State");var f=null;this.s.init.aiOrder&&(f=this.s.init.aiOrder.slice());
this.s.dt.oLoadedState&&("undefined"!=typeof this.s.dt.oLoadedState.ColReorder&&this.s.dt.oLoadedState.ColReorder.length==this.s.dt.aoColumns.length)&&(f=this.s.dt.oLoadedState.ColReorder);if(f)if(a.s.dt._bInitComplete)c=o(f),a._fnOrderColumns.call(a,c);else{var g=!1;d(e).on("draw.dt.colReorder",function(){if(!a.s.dt._bInitComplete&&!g){g=true;var b=o(f);a._fnOrderColumns.call(a,b)}})}else this._fnSetColumnIndexes();d(e).on("destroy.dt.colReorder",function(){d(e).off("destroy.dt.colReorder draw.dt.colReorder");
d(a.s.dt.nTHead).find("*").off(".ColReorder");d.each(a.s.dt.aoColumns,function(a,b){d(b.nTh).removeAttr("data-column-index")});a.s.dt._colReorder=null;a.s=null})},_fnOrderColumns:function(a){if(a.length!=this.s.dt.aoColumns.length)this.s.dt.oInstance.oApi._fnLog(this.s.dt,1,"ColReorder - array reorder does not match known number of columns. Skipping.");else{for(var c=0,e=a.length;c<e;c++){var b=d.inArray(c,a);c!=b&&(m(a,b,c),this.s.dt.oInstance.fnColReorder(b,c))}(""!==this.s.dt.oScroll.sX||""!==
this.s.dt.oScroll.sY)&&this.s.dt.oInstance.fnAdjustColumnSizing(!1);this.s.dt.oInstance.oApi._fnSaveState(this.s.dt);this._fnSetColumnIndexes();null!==this.s.reorderCallback&&this.s.reorderCallback.call(this)}},_fnStateSave:function(a){var c,e,b,f=this.s.dt.aoColumns;a.ColReorder=[];if(a.aaSorting){for(c=0;c<a.aaSorting.length;c++)a.aaSorting[c][0]=f[a.aaSorting[c][0]]._ColReorder_iOrigCol;var g=d.extend(!0,[],a.aoSearchCols);c=0;for(e=f.length;c<e;c++)b=f[c]._ColReorder_iOrigCol,a.aoSearchCols[b]=
g[c],a.abVisCols[b]=f[c].bVisible,a.ColReorder.push(b)}else if(a.order){for(c=0;c<a.order.length;c++)a.order[c][0]=f[a.order[c][0]]._ColReorder_iOrigCol;g=d.extend(!0,[],a.columns);c=0;for(e=f.length;c<e;c++)b=f[c]._ColReorder_iOrigCol,a.columns[b]=g[c],a.ColReorder.push(b)}},_fnMouseListener:function(a,c){var e=this;d(c).on("mousedown.ColReorder",function(a){a.preventDefault();e._fnMouseDown.call(e,a,c)})},_fnMouseDown:function(a,c){var e=this,b=d(a.target).closest("th, td").offset(),f=parseInt(d(c).attr("data-column-index"),
10);f!==r&&(this.s.mouse.startX=a.pageX,this.s.mouse.startY=a.pageY,this.s.mouse.offsetX=a.pageX-b.left,this.s.mouse.offsetY=a.pageY-b.top,this.s.mouse.target=this.s.dt.aoColumns[f].nTh,this.s.mouse.targetIndex=f,this.s.mouse.fromIndex=f,this._fnRegions(),d(p).on("mousemove.ColReorder",function(a){e._fnMouseMove.call(e,a)}).on("mouseup.ColReorder",function(a){e._fnMouseUp.call(e,a)}))},_fnMouseMove:function(a){if(null===this.dom.drag){if(5>Math.pow(Math.pow(a.pageX-this.s.mouse.startX,2)+Math.pow(a.pageY-
this.s.mouse.startY,2),0.5))return;this._fnCreateDragNode()}this.dom.drag.css({left:a.pageX-this.s.mouse.offsetX,top:a.pageY-this.s.mouse.offsetY});for(var c=!1,d=this.s.mouse.toIndex,b=1,f=this.s.aoTargets.length;b<f;b++)if(a.pageX<this.s.aoTargets[b-1].x+(this.s.aoTargets[b].x-this.s.aoTargets[b-1].x)/2){this.dom.pointer.css("left",this.s.aoTargets[b-1].x);this.s.mouse.toIndex=this.s.aoTargets[b-1].to;c=!0;break}c||(this.dom.pointer.css("left",this.s.aoTargets[this.s.aoTargets.length-1].x),this.s.mouse.toIndex=
this.s.aoTargets[this.s.aoTargets.length-1].to);this.s.init.bRealtime&&d!==this.s.mouse.toIndex&&(this.s.dt.oInstance.fnColReorder(this.s.mouse.fromIndex,this.s.mouse.toIndex),this.s.mouse.fromIndex=this.s.mouse.toIndex,this._fnRegions())},_fnMouseUp:function(){d(p).off("mousemove.ColReorder mouseup.ColReorder");null!==this.dom.drag&&(this.dom.drag.remove(),this.dom.pointer.remove(),this.dom.drag=null,this.dom.pointer=null,this.s.dt.oInstance.fnColReorder(this.s.mouse.fromIndex,this.s.mouse.toIndex),
this._fnSetColumnIndexes(),(""!==this.s.dt.oScroll.sX||""!==this.s.dt.oScroll.sY)&&this.s.dt.oInstance.fnAdjustColumnSizing(!1),this.s.dt.oInstance.oApi._fnSaveState(this.s.dt),null!==this.s.reorderCallback&&this.s.reorderCallback.call(this))},_fnRegions:function(){var a=this.s.dt.aoColumns;this.s.aoTargets.splice(0,this.s.aoTargets.length);this.s.aoTargets.push({x:d(this.s.dt.nTable).offset().left,to:0});for(var c=0,e=0,b=a.length;e<b;e++)e!=this.s.mouse.fromIndex&&c++,a[e].bVisible&&this.s.aoTargets.push({x:d(a[e].nTh).offset().left+
d(a[e].nTh).outerWidth(),to:c});0!==this.s.fixedRight&&this.s.aoTargets.splice(this.s.aoTargets.length-this.s.fixedRight);0!==this.s.fixed&&this.s.aoTargets.splice(0,this.s.fixed)},_fnCreateDragNode:function(){var a=""!==this.s.dt.oScroll.sX||""!==this.s.dt.oScroll.sY,c=this.s.dt.aoColumns[this.s.mouse.targetIndex].nTh,e=c.parentNode,b=e.parentNode,f=b.parentNode,g=d(c).clone();this.dom.drag=d(f.cloneNode(!1)).addClass("DTCR_clonedTable").append(d(b.cloneNode(!1)).append(d(e.cloneNode(!1)).append(g[0]))).css({position:"absolute",
top:0,left:0,width:d(c).outerWidth(),height:d(c).outerHeight()}).appendTo("body");this.dom.pointer=d("<div></div>").addClass("DTCR_pointer").css({position:"absolute",top:a?d("div.dataTables_scroll",this.s.dt.nTableWrapper).offset().top:d(this.s.dt.nTable).offset().top,height:a?d("div.dataTables_scroll",this.s.dt.nTableWrapper).height():d(this.s.dt.nTable).height()}).appendTo("body")},_fnSetColumnIndexes:function(){d.each(this.s.dt.aoColumns,function(a,c){d(c.nTh).attr("data-column-index",a)})}};g.defaults=
{aiOrder:null,bRealtime:!0,iFixedColumnsLeft:0,iFixedColumnsRight:0,fnReorderCallback:null};g.version="1.2.0";d.fn.dataTable.ColReorder=g;d.fn.DataTable.ColReorder=g;"function"==typeof d.fn.dataTable&&"function"==typeof d.fn.dataTableExt.fnVersionCheck&&d.fn.dataTableExt.fnVersionCheck("1.10.8")?d.fn.dataTableExt.aoFeatures.push({fnInit:function(a){var c=a.oInstance;a._colReorder?c.oApi._fnLog(a,1,"ColReorder attempted to initialise twice. Ignoring second"):(c=a.oInit,new g(a,c.colReorder||c.oColReorder||
{}));return null},cFeature:"R",sFeature:"ColReorder"}):alert("Warning: ColReorder requires DataTables 1.10.8 or greater - www.datatables.net/download");d(p).on("preInit.dt.colReorder",function(a,c){if("dt"===a.namespace){var e=c.oInit.colReorder,b=h.defaults.colReorder;if(e||b)b=d.extend({},e,b),!1!==e&&new g(c,b)}});d.fn.dataTable.Api.register("colReorder.reset()",function(){return this.iterator("table",function(a){a._colReorder.fnReset()})});d.fn.dataTable.Api.register("colReorder.order()",function(a){return a?
this.iterator("table",function(c){c._colReorder.fnOrder(a)}):this.context.length?this.context[0]._colReorder.fnOrder():null});return g};"function"===typeof define&&define.amd?define(["jquery","datatables"],n):"object"===typeof exports?n(require("jquery"),require("datatables")):jQuery&&!jQuery.fn.dataTable.ColReorder&&n(jQuery,jQuery.fn.dataTable)})(window,document);

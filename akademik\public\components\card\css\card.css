/*!
 * Propeller v1.0.0 (http://propeller.in)
 * Copyright 2016-2017 Digicorp, Inc.
 * Licensed under MIT (http://propeller.in/LICENSE)
*/
.pmd-z-depth{ -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24); -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24); box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);}
.pmd-z-depth-1{ -webkit-box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23); -moz-box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23); box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);}
.pmd-z-depth-2 { -webkit-box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23); -moz-box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23); box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);}
.pmd-z-depth-3 { -webkit-box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22); -moz-box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22); box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);}
.pmd-z-depth-4 { -webkit-box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22); -moz-box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22); box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);}
.pmd-z-depth-5 { -webkit-box-shadow: 0 24px 48px rgba(0,0,0,0.30), 0 20px 14px rgba(0,0,0,0.22); -moz-box-shadow: 0 24px 48px rgba(0,0,0,0.30), 0 20px 14px rgba(0,0,0,0.22); box-shadow: 0 24px 48px rgba(0,0,0,0.30), 0 20px 14px rgba(0,0,0,0.22);}

.pmd-card .form-horizontal .form-group{ margin-left:inherit; margin-right:inherit}
.pmd-card { background-color:#fff; border-radius:2px; margin-bottom:30px; background-color:#fff; padding:1px 0;}
.pmd-card-body { padding-left:16px; padding-right:16px; margin-top:16px; margin-bottom:16px; color:rgba(0, 0, 0, 0.84)}
.pmd-card-title { padding: 16px 16px 0 16px; margin-bottom:16px; border-bottom: 1px solid transparent; border-top-left-radius: 3px; border-top-right-radius: 3px;}

.pmd-card-title > .dropdown .dropdown-toggle { color: inherit;}
.pmd-card-title > .dropdown .dropdown-toggle { color: inherit;}
.pmd-card-title-text { margin-top: 0; margin-bottom: 0; color: inherit;}

h2.pmd-card-title-text{ font-size:1.5rem; font-weight:400; margin-bottom:2px;}
.pmd-card-subtitle-text { line-height: 1.6;  margin-bottom: 0;  opacity: 0.54; font-size:14px;}
.pmd-card-footer{ padding:8px 16px; display:table; content:""; width:100%;}
.pmd-card-actions{ padding:8px 4px;}
.pmd-card-actions .btn{ margin-left:4px; margin-right:4px; margin-bottom:8px;}
.pmd-card-actions .btn:first-child{margin-left:12px;}
.pmd-card-actions .btn:last-child{margin-right:12px;}
.pmd-card-actions .btn.pmd-btn-flat{ margin-left:4px; margin-right:4px; margin-bottom:0;}
.pmd-card-actions .btn{padding: 10px 8px; min-width:inherit;}
.pmd-card-actions .btn.pmd-btn-fab{padding: 0;}

/* Card Media Action */
.pmd-card-media-inline .pmd-card-media{ margin-top:16px; padding-left:16px; padding-right:16px;}
.pmd-card-media-inline .pmd-card-media h2.pmd-card-title-text{ margin-top:4px;}
.pmd-card-footer-p16 { padding-left: 20px; padding-right: 20px;}
.pmd-card-footer-no-border { border-color: transparent; padding-top: 0;}
.pmd-card-list{ padding-top:8px; padding-bottom:8px; background-color:#fff;}

/* Propeller Card */
.panel { margin-bottom: 20px;  background-color: #fff;  border: 1px solid transparent;  border-radius: 4px;  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);         box-shadow: 0 1px 1px rgba(0, 0, 0, .05);}
.panel-body { padding: 15px;}
.panel-heading { padding: 10px 15px; border-bottom: 1px solid transparent; border-top-left-radius: 3px; border-top-right-radius: 3px;}
.panel-heading > .dropdown .dropdown-toggle { color: inherit;}
.panel-title { margin-top: 0; margin-bottom: 0; font-size: 16px; color: inherit;}
.panel-title > a, .panel-title > small, .panel-title > .small, .panel-title > small > a, .panel-title > .small > a { color: inherit;}
.panel-footer { padding: 10px 15px; background-color: #f5f5f5; border-top: 1px solid #ddd; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;}

/* Card inverse*/
.pmd-card-inverse{ background-color: #373a3c; color: #eceeef;}
.pmd-card-inverse .pmd-card-body{color: rgba(255, 255, 255, 0.84);}
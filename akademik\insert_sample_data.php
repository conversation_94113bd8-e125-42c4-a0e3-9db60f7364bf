<?php
include_once __DIR__."/includes/config.php";
include_once __DIR__."/includes/class.php";

$app = new App($config);

echo "Inserting sample data...\n";

try {
    // Insert additional user (3rd user)
    $hashedPassword = password_hash("password123", PASSWORD_DEFAULT);
    $sql = "INSERT INTO pengguna (username, password, nama, level_akses) VALUES (?, ?, ?, ?)";
    $params = array("student1", $hashedPassword, "Ahmad Mahasiswa", "Mahasiswa");
    $app->query($sql, $params);
    echo "✓ User 'student1' inserted successfully\n";
    
    // Insert sample student data (my data as requested)
    $sql = "INSERT INTO mahasiswa (nim, nama, tempat_lahir, tanggal_lahir, jenis_kelamin, jurusan_id, tahun_masuk, foto) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $params = array("2024001001", "Augment Assistant", "Jakarta", "2000-01-01", "Laki-laki", 1, 2024, "default.jpg");
    $app->query($sql, $params);
    echo "✓ Student 'Augment Assistant' inserted successfully\n";
    
    echo "\nSample data insertion completed!\n";
    echo "Total users in database: 3 (admin, mfikry, student1)\n";
    echo "Total students in database: 1 (Augment Assistant)\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>

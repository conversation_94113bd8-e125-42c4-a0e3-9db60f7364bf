<!-- User name -->
<div class="form-group pmd-textfield">
	<label for="inputError1" class="control-label pmd-input-group-label">Username</label>
	<div class="input-group">
		<div class="input-group-addon"><i class="material-icons md-dark pmd-sm">perm_identity</i></div>
		<input type="text" class="form-control" id="exampleInputAmount">
	</div>
</div>

<!-- Password -->
<div class="form-group pmd-textfield">
	<label for="inputError1" class="control-label pmd-input-group-label">Password</label>
	<div class="input-group">
		<div class="input-group-addon"><i class="material-icons md-dark pmd-sm">https</i></div>
		<input type="password" class="form-control" id="exampleInputAmount">
	</div>
</div>

<!-- User name with floating label -->
<div class="form-group pmd-textfield pmd-textfield-floating-label">
	<label for="inputError1" class="control-label pmd-input-group-label">Username</label>
	<div class="input-group">
		<div class="input-group-addon"><i class="material-icons md-dark pmd-sm">perm_identity</i></div>
		<input type="text" class="form-control" id="exampleInputAmount">
	</div>
</div>

<!-- Password with floating label -->
<div class="form-group pmd-textfield pmd-textfield-floating-label">
	<label for="inputError1" class="control-label pmd-input-group-label">Password</label>
	<div class="input-group">
		<div class="input-group-addon"><i class="material-icons md-dark pmd-sm">https</i></div>
		<input type="password" class="form-control" id="exampleInputAmount">
	</div>
</div>
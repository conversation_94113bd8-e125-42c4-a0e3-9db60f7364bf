<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="//code.jquery.com/jquery-1.11.3.min.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>

	<title>BUttons examples - Column visibility examples</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>BUttons example <span>Column visibility examples</span></h1>

			<div class="info">
				<p>The column visibility plug-in for the DataTables Buttons extension provides a suite of buttons that can be used to very easily control the visibility of columns
				in a table.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Column Visibility</a></h3>
						<ul class="toc">
							<li><a href="./simple.html">Basic column visibility</a></li>
							<li><a href="./layout.html">Multi-column layout</a></li>
							<li><a href="./text.html">Internationalisation</a></li>
							<li><a href="./restore.html">Restore column visibility</a></li>
							<li><a href="./columns.html">Select columns</a></li>
							<li><a href="./columnsToggle.html">Visibility toggle buttons</a></li>
							<li><a href="./columnGroups.html">Column groups</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>

	<title>Buttons examples - Flash export buttons</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Buttons example <span>Flash export buttons</span></h1>

			<div class="info">
				<p>The Flash export buttons provide an option for legacy browsers (IE9-) to create and save files on the client-side, without any server interaction required.</p>

				<p>The HTML5 button types are preferred over the Flash buttons as they do not require Adobe Flash and are generally more configurable, however, not all browsers
				provide the functionality required for those buttons.</p>

				<p>If making use of these buttons in browsers which do not have Flash installed and enabled these buttons will not appear on the end user's page (no errors will be
				generated).</p>

				<p>The examples in this section explore the options available for the Flash export buttons.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Flash</a></h3>
						<ul class="toc">
							<li><a href="./simple.html">Flash export buttons</a></li>
							<li><a href="./tsv.html">Tab separated values</a></li>
							<li><a href="./filename.html">File name</a></li>
							<li><a href="./copyi18n.html">Copy button internationalisation</a></li>
							<li><a href="./pdfMessage.html">PDF message</a></li>
							<li><a href="./pdfPage.html">Page size and orientation</a></li>
							<li><a href="./hidden.html">Hidden initialisation</a></li>
							<li><a href="./swfPath.html">SWF file location</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
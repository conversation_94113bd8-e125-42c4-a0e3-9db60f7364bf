<!doctype html>
<html><head>
<meta charset="utf-8">
<meta content="width=device-width, initial-scale=1, user-scalable=no" name="viewport">
<title>Menu</title>

<link rel="stylesheet" type="text/css" href="../../../assets/css/bootstrap.css">
<link rel="stylesheet" type="text/css" href="../../typography/css/typography.css">
<link rel="stylesheet" type="text/css" href="../css/navbar.css">
<link rel="stylesheet" type="text/css" href="../../dropdown/css/dropdown.css">
<link rel="stylesheet" type="text/css" href="../../button/css/button.css">
<link rel="stylesheet" type="text/css" href="../../icons/css/google-icons.css">

</head>

<body>
	<nav class="navbar navbar-inverse navbar-fixed-top pmd-navbar pmd-z-depth">
      <div class="container-fluid">
        <!-- Brand and toggle get grouped for better mobile display -->
		
        <div class="navbar-header">
          <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a href="javascript:void(0);" class="navbar-brand navbar-brand-custome">Brand</a>
        </div>

        <!-- Collect the nav links, forms, and other content for toggling -->
        <div id="bs-example-navbar-collapse-1" class="collapse navbar-collapse">
	      <ul class="nav navbar-nav">
            <li><a class="pmd-ripple-effect" href="javascript:void(0);">Link <span class="sr-only">(current)</span></a></li>
            <li><a class="pmd-ripple-effect" href="javascript:void(0);">Link</a></li>
            <li class="dropdown pmd-dropdown">
              <a data-toggle="dropdown" class="pmd-ripple-effect dropdown-toggle" data-sidebar="true" href="javascript:void(0);">Dropdown <span class="caret"></span></a>
              <ul class="dropdown-menu">
                <li><a class="pmd-ripple-effect" href="javascript:void(0);">Action</a></li>
                <li><a class="pmd-ripple-effect" href="javascript:void(0);">Another action</a></li>
                <li><a class="pmd-ripple-effect" href="javascript:void(0);">Something else here</a></li>
                <li class="divider"></li>
                <li><a class="pmd-ripple-effect" href="javascript:void(0);">Separated link</a></li>
                <li class="divider"></li>
                <li><a class="pmd-ripple-effect" href="javascript:void(0);">One more separated link</a></li>
              </ul>
            </li>
          </ul>
        </div><!-- /.navbar-collapse -->
		
      </div><!-- /.container-fluid -->
	  <div class="pmd-sidebar-overlay"></div>
    </nav>

<script src="../../../assets/js/jquery.js"></script>
<script src="../../../assets/js/bootstrap.js"></script>
<script src="../../sidebar/js/sidebar.js"></script>
<script src="../../../assets/js/propeller.min.js"></script>

</body>
</html>

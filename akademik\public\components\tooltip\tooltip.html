<!doctype html>
<html lang="">
<head>
 	<meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="Propeller is a front-end responsive framework based on Material design & Bootstrap.">
	<meta content="width=device-width, initial-scale=1, user-scalable=no" name="viewport">
    <title>Tooltip - Propeller Components</title>
    
	<!-- favicon --> 
	<link rel="icon" href="http://propeller.in/assets/images/favicon.ico" type="image/x-icon">
	
	<!-- Bootstrap --> 
	<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" type="text/css" rel="stylesheet" /> 
	
	<!-- Propeller card (CSS for helping component example file)-->
	<link href="http://propeller.in/components/card/css/card.css" type="text/css" rel="stylesheet" />
	
	<!-- Example docs (CSS for helping component example file) -->
	<link href="http://propeller.in/docs/css/example-docs.css" type="text/css" rel="stylesheet" />
	
	<!-- Propeller typography (CSS for helping component example file) -->
	<link href="http://propeller.in/components/typography/css/typography.css" type="text/css" rel="stylesheet" />
	
	<!-- Propeller Button -->
	<link href="http://propeller.in/components/button/css/button.css" type="text/css" rel="stylesheet" />
	
	<!-- Propeller tooltip -->
	<link href="css/tooltip.css" type="text/css" rel="stylesheet" /> 

</head>

<body>

	<!--Tooltip -->
	<div class="pmd-content pmd-content-custom tooltip-component" id="content">
	
		<!-- component header -->
		<div class="componant-title-bg">
			<div class="container">
				<div class="row">
				
					<!-- component title and description -->
					<div class="col-xs-12">
						<h1>Tooltip</h1>
						<p class="lead">Tooltip is a small "hover box" containing information about the item being hovered over. Tooltip appears on hover over an element with the cursor, or focus on an element using a keyboard (usually through the tab key), or upon touch (without releasing) in a touch UI.</p>
					</div><!--end component title and description -->
					
				</div>
			</div>
		</div><!-- end component header -->
		
		<div class="container">
		
			<!-- Basic Bootstrap Tooltip -->
			<section class="row component-section">
			
				<!-- Basic Bootstrap Tooltip title and description -->
				<div class="col-md-3">
					<div id="bootbasic">
						<h2>Bootstrap Tooltip</h2>
					</div>
					<p>It is an updated version, which don't rely on images, use CSS3 for animations, and data-attributes for local title storage. Tooltip with zero-length titles is never displayed.</p>
				</div><!-- end Basic Bootstrap Tooltip title and description -->
				
				<!-- Basic Bootstrap Tooltip code and example -->
				<div class="col-md-9">
					<div class="component-box">
						<!-- Basic Bootstrap Tooltip example -->
						<div class="row">
							<div class="col-md-12 tooltip-card-custom">
								<div class="pmd-card pmd-z-depth">
									<div class="pmd-card-body text-center">
										<!--Tooltip on left -->
										<button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="left" title="Tooltip on left">Tooltip on left</button>
										<!--Tooltip on right -->
										<button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="right" title="Tooltip on right">Tooltip on right</button>
										<!--Tooltip on top -->
										<button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="top" title="Tooltip on top">Tooltip on top</button>
										<!--Tooltip on bottom -->
										<button type="button" class="btn btn-default" data-toggle="tooltip" data-placement="bottom" title="Tooltip on bottom">Tooltip on bottom</button>
									</div>
								</div>
							</div>
						</div><!-- Basic Bootstrap Tooltip example end-->
						
					</div>
				</div><!-- Basic Bootstrap Tooltip code and example end-->
				
			</section><!--end Basic Bootstrap Tooltip -->
	
			<!-- Propeller Tooltip -->
			<section class="row component-section">
			
				<!-- Propeller Tooltip title and description -->
				<div class="col-md-3">
					<div id="basic">
						<h2>Propeller Tooltip</h2>
					</div>
					<p>Propeller tooltip uses basic Bootstrap Tooltip customized with Material Standards. To initialize the Propeller Tooltip, add the HTML attribute <code>data-toggle="tooltip"</code>. Add <code>.pmd-tooltip</code> to apply propeller theme and animations. To define the tooltip position, use <code>data-placement</code> attribute. <code>data-placement</code> attribute can take left, right, top and bottom as values.</p>
				</div><!-- end Propeller Tooltip title and description -->
				
				<!-- Propeller Tooltip code and example -->
				<div class="col-md-9">
					<div class="component-box">
						<!-- Propeller Tooltip example -->
						<div class="row">
							<div class="col-md-12 tooltip-card-custom">
								<div class="pmd-card pmd-z-depth">
									<div class="pmd-card-body text-center">
										<!--Tooltip on left -->
										<button type="button" class="btn pmd-btn-raised pmd-tooltip btn-default" data-toggle="tooltip" data-placement="left" title="Tooltip on left">Tooltip on left</button>
										<!--Tooltip on right -->
										<button type="button" class="btn pmd-btn-raised pmd-tooltip btn-default" data-toggle="tooltip" data-placement="right" title="Tooltip on right">Tooltip on right</button>
										<!--Tooltip on top -->
										<button type="button" class="btn pmd-btn-raised pmd-tooltip btn-default" data-toggle="tooltip" data-placement="top" title="Tooltip on top">Tooltip on top</button>
										<!--Tooltip on bottom -->
										<button type="button" class="btn pmd-btn-raised pmd-tooltip btn-default" data-toggle="tooltip" data-placement="bottom" title="Tooltip on bottom">Tooltip on bottom</button>
									</div>
								</div>
							</div>
							<div class="col-md-12">
								<p>Hover over the link below to see tooltip</p>
								<div class="pmd-card pmd-z-depth">
									<div class="pmd-card-body">
										<p style="color:rgba(0,0,0,0.54)">Propeller is the combination of <a href="javascript:void(0);" class="pmd-tooltip" data-toggle="tooltip" data-placement="top" title="Material design is a visual language for our users that synthesizes the classic principles of good design with the innovation and possibility of technology and science."><strong>Material Design</strong></a> and <a href="javascript:void(0);" class="pmd-tooltip" data-toggle="tooltip" data-placement="bottom" title="Bootstrap is the most popular HTML, CSS, and JS framework in the world for building responsive, mobile-first projects on the web."><strong>Bootstrap</strong></a> that makes your website more attractive, consistent, and functionally powerful. The Propeller components are created with CSS, JavaScript, and HTML. You can use the components to construct web pages and web apps that are attractive, consistent, and functional. By using Propeller components, you can get basic structure of your design and also customise it easily. </p>
									</div>
								</div>
							</div>
						</div><!-- Propeller Tooltip example end-->
						
					</div>
				</div><!-- Propeller Tooltip code and example end-->
				
			</section><!--end Propeller Tooltip -->
			
			<!-- Configuration starts-->        
			<section class="row component-section">
				<div class="col-md-3">
					<div id="config">
						<h2>Configuration Options</h2>
					</div>
					<p> The table lists some HTML attributes that are required for positioning and styling of the tooltip.</p>
				</div>
				
				<div class="col-md-9">
					<div class="pmd-card pmd-table-card-responsive">
						<div class="pmd-table-card">
							<table class="table pmd-table table-hover">
								<thead>
									<tr>
										<th>Propeller  Class</th>
										<th>Effect</th>
										<th>Remark</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-title="Class"><code>.pmd-tooltip</code></td>
										<td data-title="Effect">Defines Propeller theme and animation.</td>
										<td data-title="Remark">Required</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class="pmd-card pmd-table-card-responsive">
						<div class="pmd-table-card">  
							<table class="table pmd-table table-hover">
								<thead>
									<tr>
										<th>HTML Attributes</th>
										<th>Effect</th>
										<th>Value</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-title="HTML Attributes"><code>data-toogle</code></td>
										<td data-title="Effect">To initialize tooltip. </td>
										<td data-title="Value">tooltip</td>
									</tr>
									<tr>
										<td data-title="HTML Attributes"><code>data-placement</code></td>
										<td data-title="Effect">Defines the position of the tooltip. Values that can be provided are left, right, top or bottom. </td>
										<td data-title="Value">left, right, top, bottom</td>
									</tr>
									<tr>
										<td data-title="HTML Attributes"><code>title</code></td>
										<td data-title="Effect">Defines the content of the tooltip.</td>
										<td data-title="Value">text, HTML</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</section><!-- Configuration ends-->
			
			
			<!-- Events starts-->        
			<section class="row component-section">
				<div class="col-md-3">
					<div id="events">
						<h2>Events</h2>
					</div>
					<p>See <a href="http://getbootstrap.com/javascript/#tooltips" target="_blank">here</a> for more documentation on this.</p>
				</div>
				<div class="col-md-9">
					<!--Events card start -->
					<div class="pmd-card pmd-table-card-responsive">
						<div class="pmd-table-card">  
							<table class="table pmd-table table-hover">
								<thead>
									<tr>
										<th>Event Type</th>
										<th>Description</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-title="Class"><code>show.bs.tooltip</code></td>
										<td data-title="Effect">This event fires immediately when the <code>show</code> instance method is called.</td>
									</tr>
									<tr>
										<td data-title="Class"><code>shown.bs.tooltip</code></td>
										<td data-title="Effect">This event is fired when the tooltip has been made visible to the user (will wait for CSS transitions to complete).</td>
									</tr>
									<tr>
										<td data-title="Class"><code>hide.bs.tooltip</code></td>
										<td data-title="Effect">This event is fired immediately when the <code>hide</code> instance method has been called.</td>
									</tr>
									<tr>
										<td data-title="Class"><code>hidden.bs.tooltip</code></td>
										<td data-title="Effect">This event is fired when the tooltip has finished being hidden from the user (will wait for CSS transitions to complete).</td>
									</tr>
									<tr>
										<td data-title="Class"><code>inserted.bs.tooltip</code></td>
										<td data-title="Effect">This event is fired after the <code>show.bs.tooltip</code> event when the tooltip template has been added to the DOM.</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</section><!-- Events ends-->   
			
		</div><!--container tooltip end -->
		
	</div><!--Tooltip constructor end -->	

</body>
<!-- Jquery js -->
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>

<!-- Bootstrap js -->
<script type="text/javascript" src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script>
$(function () {
  $('[data-toggle="tooltip"]').tooltip()
})
</script>
</html>


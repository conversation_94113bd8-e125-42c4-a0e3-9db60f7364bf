/* Bootstrap css */
@import "https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css";

HTML{ font-size:16px;}

/* Headings */
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 { font-weight: normal;}

/* small secondary heading text */
h1 small, h2 small, h3 small, h4 small, h5 small, 
h6 small, .h1 small, .h2 small, .h3 small, .h4 small,
.h5 small, .h6 small, h1 .small, h2 .small, h3 .small, 
h4 .small, h5 .small, h6 .small, .h1 .small, .h2 .small, 
.h3 .small, .h4 .small, .h5 .small, .h6 .small { color:rgba(0,0,0,0.54);}

/* Font size and color according to Google Material */
h1, .h1 { font-size: 1.5rem;}
h2, .h2 { font-size: 1.25rem; font-weight:500;}
h3, .h3 { font-size: 1rem;}
h4, .h4 { font-size: 0.8125rem; font-weight:500;}
h5, .h5 { font-size: 0.8125rem;}

/* Type display classes */
.pmd-display1{font-size: 2.125rem; opacity:0.54; font-weight:normal;}
.pmd-display2{font-size: 2.8125rem; opacity:0.54; font-weight:normal;}
.pmd-display3{font-size: 3.5rem; opacity:0.54; font-weight:normal;}
.pmd-display4{font-size: 7rem; font-weight:300; opacity:0.54;}

.pmd-caption {color: rgba(0, 0, 0, 0.54); font-size: 0.75rem; letter-spacing: 0.02em;}

/*Desciption list*/
.dl-horizontal dt{ margin-bottom:20px;}
.dl-horizontal dd{ margin-left:0;}
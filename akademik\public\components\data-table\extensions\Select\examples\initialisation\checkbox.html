<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">

	<title>Select example - Checkbox selection</title>
	<link rel="stylesheet" type="text/css" href="../../../../media/css/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../../css/select.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<style type="text/css" class="init">

	</style>
	<script type="text/javascript" language="javascript" src="//code.jquery.com/jquery-1.11.3.min.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../media/js/jquery.dataTables.js"></script>
	<script type="text/javascript" language="javascript" src="../../js/dataTables.select.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>
	<script type="text/javascript" language="javascript" class="init">



$(document).ready(function() {
	$('#example').DataTable( {
		columnDefs: [ {
			orderable: false,
			className: 'select-checkbox',
			targets:   0
		} ],
		select: {
			style:    'os',
			selector: 'td:first-child'
		},
		order: [[ 1, 'asc' ]]
	} );
} );



	</script>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Select example <span>Checkbox selection</span></h1>

			<div class="info">
				<p>A selected row is typically shown in a DataTable by using a highlight background colour - however, it can also be useful to use other styling options to convey
				the selected state of items in a table to the end user.</p>

				<p>A common option is to use a checkbox which can be clicked on to toggle row selection, which can be particularly useful if you wish to restrict row selection
				activation to a particular column, so other actions can be performed on the other cells in the table (such as <a href=
				"//editor.datatables.net/examples/inline-editing/simple">inline editing</a>).</p>

				<p>A column can be shown with a checkbox that reflects the row's selected status simply through use of the <code class="string" title=
				"String">select-checkbox</code> CSS class for that column (<a href="//datatables.net/reference/option/columns.className"><code class="option" title=
				"DataTables initialisation option">columns.className</code></a>). Row selection can be restricted to that column using the <a href=
				"//datatables.net/reference/option/select.selector"><code class="option" title="Scroller initialisation option">select.selector</code></a> option.</p>

				<p>The checkbox is not an <code>&lt;input type="checkbox"&gt;</code> element, but rather a CSS that uses the <code>:before</code> and <code>:after</code> pseudo
				elements of the cell to draw a box and the tick. This can therefore be easily modified to suit the style of your site / app.</p>
			</div>

			<table id="example" class="display" cellspacing="0" width="100%">
				<thead>
					<tr>
						<th></th>
						<th>Name</th>
						<th>Position</th>
						<th>Office</th>
						<th>Age</th>
						<th>Salary</th>
					</tr>
				</thead>

				<tfoot>
					<tr>
						<th></th>
						<th>Name</th>
						<th>Position</th>
						<th>Office</th>
						<th>Age</th>
						<th>Salary</th>
					</tr>
				</tfoot>

				<tbody>
					<tr>
						<td></td>
						<td>Tiger Nixon</td>
						<td>System Architect</td>
						<td>Edinburgh</td>
						<td>61</td>
						<td>$320,800</td>
					</tr>
					<tr>
						<td></td>
						<td>Garrett Winters</td>
						<td>Accountant</td>
						<td>Tokyo</td>
						<td>63</td>
						<td>$170,750</td>
					</tr>
					<tr>
						<td></td>
						<td>Ashton Cox</td>
						<td>Junior Technical Author</td>
						<td>San Francisco</td>
						<td>66</td>
						<td>$86,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Cedric Kelly</td>
						<td>Senior Javascript Developer</td>
						<td>Edinburgh</td>
						<td>22</td>
						<td>$433,060</td>
					</tr>
					<tr>
						<td></td>
						<td>Airi Satou</td>
						<td>Accountant</td>
						<td>Tokyo</td>
						<td>33</td>
						<td>$162,700</td>
					</tr>
					<tr>
						<td></td>
						<td>Brielle Williamson</td>
						<td>Integration Specialist</td>
						<td>New York</td>
						<td>61</td>
						<td>$372,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Herrod Chandler</td>
						<td>Sales Assistant</td>
						<td>San Francisco</td>
						<td>59</td>
						<td>$137,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Rhona Davidson</td>
						<td>Integration Specialist</td>
						<td>Tokyo</td>
						<td>55</td>
						<td>$327,900</td>
					</tr>
					<tr>
						<td></td>
						<td>Colleen Hurst</td>
						<td>Javascript Developer</td>
						<td>San Francisco</td>
						<td>39</td>
						<td>$205,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Sonya Frost</td>
						<td>Software Engineer</td>
						<td>Edinburgh</td>
						<td>23</td>
						<td>$103,600</td>
					</tr>
					<tr>
						<td></td>
						<td>Jena Gaines</td>
						<td>Office Manager</td>
						<td>London</td>
						<td>30</td>
						<td>$90,560</td>
					</tr>
					<tr>
						<td></td>
						<td>Quinn Flynn</td>
						<td>Support Lead</td>
						<td>Edinburgh</td>
						<td>22</td>
						<td>$342,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Charde Marshall</td>
						<td>Regional Director</td>
						<td>San Francisco</td>
						<td>36</td>
						<td>$470,600</td>
					</tr>
					<tr>
						<td></td>
						<td>Haley Kennedy</td>
						<td>Senior Marketing Designer</td>
						<td>London</td>
						<td>43</td>
						<td>$313,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Tatyana Fitzpatrick</td>
						<td>Regional Director</td>
						<td>London</td>
						<td>19</td>
						<td>$385,750</td>
					</tr>
					<tr>
						<td></td>
						<td>Michael Silva</td>
						<td>Marketing Designer</td>
						<td>London</td>
						<td>66</td>
						<td>$198,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Paul Byrd</td>
						<td>Chief Financial Officer (CFO)</td>
						<td>New York</td>
						<td>64</td>
						<td>$725,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Gloria Little</td>
						<td>Systems Administrator</td>
						<td>New York</td>
						<td>59</td>
						<td>$237,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Bradley Greer</td>
						<td>Software Engineer</td>
						<td>London</td>
						<td>41</td>
						<td>$132,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Dai Rios</td>
						<td>Personnel Lead</td>
						<td>Edinburgh</td>
						<td>35</td>
						<td>$217,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Jenette Caldwell</td>
						<td>Development Lead</td>
						<td>New York</td>
						<td>30</td>
						<td>$345,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Yuri Berry</td>
						<td>Chief Marketing Officer (CMO)</td>
						<td>New York</td>
						<td>40</td>
						<td>$675,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Caesar Vance</td>
						<td>Pre-Sales Support</td>
						<td>New York</td>
						<td>21</td>
						<td>$106,450</td>
					</tr>
					<tr>
						<td></td>
						<td>Doris Wilder</td>
						<td>Sales Assistant</td>
						<td>Sidney</td>
						<td>23</td>
						<td>$85,600</td>
					</tr>
					<tr>
						<td></td>
						<td>Angelica Ramos</td>
						<td>Chief Executive Officer (CEO)</td>
						<td>London</td>
						<td>47</td>
						<td>$1,200,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Gavin Joyce</td>
						<td>Developer</td>
						<td>Edinburgh</td>
						<td>42</td>
						<td>$92,575</td>
					</tr>
					<tr>
						<td></td>
						<td>Jennifer Chang</td>
						<td>Regional Director</td>
						<td>Singapore</td>
						<td>28</td>
						<td>$357,650</td>
					</tr>
					<tr>
						<td></td>
						<td>Brenden Wagner</td>
						<td>Software Engineer</td>
						<td>San Francisco</td>
						<td>28</td>
						<td>$206,850</td>
					</tr>
					<tr>
						<td></td>
						<td>Fiona Green</td>
						<td>Chief Operating Officer (COO)</td>
						<td>San Francisco</td>
						<td>48</td>
						<td>$850,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Shou Itou</td>
						<td>Regional Marketing</td>
						<td>Tokyo</td>
						<td>20</td>
						<td>$163,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Michelle House</td>
						<td>Integration Specialist</td>
						<td>Sidney</td>
						<td>37</td>
						<td>$95,400</td>
					</tr>
					<tr>
						<td></td>
						<td>Suki Burks</td>
						<td>Developer</td>
						<td>London</td>
						<td>53</td>
						<td>$114,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Prescott Bartlett</td>
						<td>Technical Author</td>
						<td>London</td>
						<td>27</td>
						<td>$145,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Gavin Cortez</td>
						<td>Team Leader</td>
						<td>San Francisco</td>
						<td>22</td>
						<td>$235,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Martena Mccray</td>
						<td>Post-Sales support</td>
						<td>Edinburgh</td>
						<td>46</td>
						<td>$324,050</td>
					</tr>
					<tr>
						<td></td>
						<td>Unity Butler</td>
						<td>Marketing Designer</td>
						<td>San Francisco</td>
						<td>47</td>
						<td>$85,675</td>
					</tr>
					<tr>
						<td></td>
						<td>Howard Hatfield</td>
						<td>Office Manager</td>
						<td>San Francisco</td>
						<td>51</td>
						<td>$164,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Hope Fuentes</td>
						<td>Secretary</td>
						<td>San Francisco</td>
						<td>41</td>
						<td>$109,850</td>
					</tr>
					<tr>
						<td></td>
						<td>Vivian Harrell</td>
						<td>Financial Controller</td>
						<td>San Francisco</td>
						<td>62</td>
						<td>$452,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Timothy Mooney</td>
						<td>Office Manager</td>
						<td>London</td>
						<td>37</td>
						<td>$136,200</td>
					</tr>
					<tr>
						<td></td>
						<td>Jackson Bradshaw</td>
						<td>Director</td>
						<td>New York</td>
						<td>65</td>
						<td>$645,750</td>
					</tr>
					<tr>
						<td></td>
						<td>Olivia Liang</td>
						<td>Support Engineer</td>
						<td>Singapore</td>
						<td>64</td>
						<td>$234,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Bruno Nash</td>
						<td>Software Engineer</td>
						<td>London</td>
						<td>38</td>
						<td>$163,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Sakura Yamamoto</td>
						<td>Support Engineer</td>
						<td>Tokyo</td>
						<td>37</td>
						<td>$139,575</td>
					</tr>
					<tr>
						<td></td>
						<td>Thor Walton</td>
						<td>Developer</td>
						<td>New York</td>
						<td>61</td>
						<td>$98,540</td>
					</tr>
					<tr>
						<td></td>
						<td>Finn Camacho</td>
						<td>Support Engineer</td>
						<td>San Francisco</td>
						<td>47</td>
						<td>$87,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Serge Baldwin</td>
						<td>Data Coordinator</td>
						<td>Singapore</td>
						<td>64</td>
						<td>$138,575</td>
					</tr>
					<tr>
						<td></td>
						<td>Zenaida Frank</td>
						<td>Software Engineer</td>
						<td>New York</td>
						<td>63</td>
						<td>$125,250</td>
					</tr>
					<tr>
						<td></td>
						<td>Zorita Serrano</td>
						<td>Software Engineer</td>
						<td>San Francisco</td>
						<td>56</td>
						<td>$115,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Jennifer Acosta</td>
						<td>Junior Javascript Developer</td>
						<td>Edinburgh</td>
						<td>43</td>
						<td>$75,650</td>
					</tr>
					<tr>
						<td></td>
						<td>Cara Stevens</td>
						<td>Sales Assistant</td>
						<td>New York</td>
						<td>46</td>
						<td>$145,600</td>
					</tr>
					<tr>
						<td></td>
						<td>Hermione Butler</td>
						<td>Regional Director</td>
						<td>London</td>
						<td>47</td>
						<td>$356,250</td>
					</tr>
					<tr>
						<td></td>
						<td>Lael Greer</td>
						<td>Systems Administrator</td>
						<td>London</td>
						<td>21</td>
						<td>$103,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Jonas Alexander</td>
						<td>Developer</td>
						<td>San Francisco</td>
						<td>30</td>
						<td>$86,500</td>
					</tr>
					<tr>
						<td></td>
						<td>Shad Decker</td>
						<td>Regional Director</td>
						<td>Edinburgh</td>
						<td>51</td>
						<td>$183,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Michael Bruce</td>
						<td>Javascript Developer</td>
						<td>Singapore</td>
						<td>29</td>
						<td>$183,000</td>
					</tr>
					<tr>
						<td></td>
						<td>Donna Snider</td>
						<td>Customer Support</td>
						<td>New York</td>
						<td>27</td>
						<td>$112,000</td>
					</tr>
				</tbody>
			</table>

			<ul class="tabs">
				<li class="active">Javascript</li>
				<li>HTML</li>
				<li>CSS</li>
				<li>Ajax</li>
				<li>Server-side script</li>
			</ul>

			<div class="tabs">
				<div class="js">
					<p>The Javascript shown below is used to initialise the table shown in this example:</p><code class="multiline language-js">$(document).ready(function() {
	$('#example').DataTable( {
		columnDefs: [ {
			orderable: false,
			className: 'select-checkbox',
			targets:   0
		} ],
		select: {
			style:    'os',
			selector: 'td:first-child'
		},
		order: [[ 1, 'asc' ]]
	} );
} );</code>

					<p>In addition to the above code, the following Javascript library files are loaded for use in this example:</p>

					<ul>
						<li><a href="//code.jquery.com/jquery-1.11.3.min.js">//code.jquery.com/jquery-1.11.3.min.js</a></li>
						<li><a href="../../../../media/js/jquery.dataTables.js">../../../../media/js/jquery.dataTables.js</a></li>
						<li><a href="../../js/dataTables.select.js">../../js/dataTables.select.js</a></li>
					</ul>
				</div>

				<div class="table">
					<p>The HTML shown below is the raw HTML table element, before it has been enhanced by DataTables:</p>
				</div>

				<div class="css">
					<div>
						<p>This example uses a little bit of additional CSS beyond what is loaded from the library files (below), in order to correctly display the table. The
						additional CSS used is shown below:</p><code class="multiline language-css"></code>
					</div>

					<p>The following CSS library files are loaded for use in this example to provide the styling of the table:</p>

					<ul>
						<li><a href="../../../../media/css/jquery.dataTables.css">../../../../media/css/jquery.dataTables.css</a></li>
						<li><a href="../../css/select.dataTables.css">../../css/select.dataTables.css</a></li>
					</ul>
				</div>

				<div class="ajax">
					<p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data will update automatically as any additional data is
					loaded.</p>
				</div>

				<div class="php">
					<p>The script used to perform the server-side processing for this table is shown below. Please note that this is just an example script using PHP. Server-side
					processing scripts can be written in any language, using <a href="//datatables.net/manual/server-side">the protocol described in the DataTables
					documentation</a>.</p>
				</div>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<h2>Other examples</h2>

				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Initialisation</a></h3>
						<ul class="toc active">
							<li><a href="./simple.html">Simple initialisation</a></li>
							<li><a href="./single.html">Single item selection</a></li>
							<li><a href="./multi.html">Multi item selection</a></li>
							<li><a href="./cells.html">Cell selection</a></li>
							<li class="active"><a href="./checkbox.html">Checkbox selection</a></li>
							<li><a href="./i18n.html">Internationalisation</a></li>
							<li><a href="./blurable.html">Blur selection</a></li>
							<li><a href="./deferRender.html">Defer rendering</a></li>
							<li><a href="./buttons.html">Buttons</a></li>
							<li><a href="./reload.html">Retain selection on reload</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="../styling/bootstrap.html">Bootstrap styling</a></li>
							<li><a href="../styling/foundation.html">Foundation styling</a></li>
							<li><a href="../styling/jqueryui.html">jQuery UI styling</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="../api/index.html">API</a></h3>
						<ul class="toc">
							<li><a href="../api/events.html">Events</a></li>
							<li><a href="../api/get.html">Get selected items</a></li>
							<li><a href="../api/select.html">Select items</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>

	<title>Buttons examples - Initialisation</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Buttons example <span>Initialisation</span></h1>

			<div class="info">
				<p>Buttons is an unimaginatively named library that provides buttons for DataTables. Simple, single click buttons can be useful when used in combination with a
				table providing end users with the ability to interact with the table. This can take the form of exporting data, controlling column visibility, triggering an
				editing action or activating any other custom method you wish to use.</p>

				<p>The set of examples available here show how Buttons can be initialised and the basics of how to use it.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Initialisation</a></h3>
						<ul class="toc">
							<li><a href="./simple.html">Basic initialisation</a></li>
							<li><a href="./export.html">File export</a></li>
							<li><a href="./custom.html">Custom button</a></li>
							<li><a href="./className.html">Class names</a></li>
							<li><a href="./keys.html">Keyboard activation</a></li>
							<li><a href="./collections.html">Collections</a></li>
							<li><a href="./plugins.html">Plug-ins</a></li>
							<li><a href="./new.html">`new` initialisation</a></li>
							<li><a href="./multiple.html">Multiple button groups</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
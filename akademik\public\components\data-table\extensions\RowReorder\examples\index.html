<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>RowReorder examples - RowReorder examples</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>RowReorder example <span><PERSON><PERSON><PERSON><PERSON> examples</span></h1>

			<div class="info">
				<p><PERSON><PERSON><PERSON><PERSON> adds the ability for rows in a DataTable to be reordered through user interaction with the table (click and drag / touch and drag). Integration with
				Editor's multi-row editing feature is also available to update rows immediately.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./initialisation/index.html">Initialisation</a></h3>
						<ul class="toc">
							<li><a href="./initialisation/simple.html">Basic initialisation</a></li>
							<li><a href="./initialisation/restrictedOrdering.html">Restricted column ordering</a></li>
							<li><a href="./initialisation/responsive.html">Mobile support (Responsive integration)</a></li>
							<li><a href="./initialisation/selector.html">Full row selection</a></li>
							<li><a href="./initialisation/events.html">Reorder event</a></li>
							<li><a href="./initialisation/defaults.html">Defaults</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="./styling/reorderClass.html">Selector cell styling</a></li>
							<li><a href="./styling/snapX.html">Horizontal snap</a></li>
							<li><a href="./styling/bootstrap.html">Bootstrap styling</a></li>
							<li><a href="./styling/foundation.html">Foundation styling</a></li>
							<li><a href="./styling/jqueryui.html">jQuery UI styling</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
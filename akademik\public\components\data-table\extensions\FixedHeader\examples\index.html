<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="//code.jquery.com/jquery-1.11.3.min.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>FixedHeader examples - Examples index</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>FixedHeader example <span>Examples index</span></h1>

			<div class="info">
				<p>The following examples show how FixedHeader can be used via its configuration and styling options.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./options/index.html">Initialisation and options</a></h3>
						<ul class="toc">
							<li><a href="./options/simple.html">Basic initialisation</a></li>
							<li><a href="./options/header_footer.html">Header and footer fixed</a></li>
							<li><a href="./options/two_tables.html">Multiple tables</a></li>
							<li><a href="./options/offset.html">Offset</a></li>
							<li><a href="./options/enable-disable.html">Enable / disable FixedHeader</a></li>
							<li><a href="./options/new.html">Alternative initialisation</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="./styling/bootstrap.html">Bootstrap</a></li>
							<li><a href="./styling/foundation.html">Foundation</a></li>
							<li><a href="./styling/jqueryui.html">jQuery UI styling</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./integration/index.html">Integration with other DataTables extensions</a></h3>
						<ul class="toc">
							<li><a href="./integration/responsive.html">Responsive integration</a></li>
							<li><a href="./integration/colreorder.html">ColReorder integration</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
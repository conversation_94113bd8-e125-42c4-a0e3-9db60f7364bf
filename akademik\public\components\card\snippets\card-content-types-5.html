<!--Title, Media, Description and Action area -->
<!-- Inverse card -->
<div class="pmd-card pmd-card-inverse pmd-z-depth">

	<!-- Card header -->
    <div class="pmd-card-title">
      <div class="media-left">
        <a href="javascript:void(0);" class="avatar-list-img">
            <img width="40" height="40" src="http://propeller.in/assets/images/avatar-icon-40x40.png">
        </a>
      </div>
      <div class="media-body media-middle">
        <h3 class="pmd-card-title-text">Two-line item</h3>
        <span class="pmd-card-subtitle-text">Secondary text</span>
      </div>
    </div>
    
    <!-- Card media -->
    <div class="pmd-card-media">
        <img width="1184" height="666" class="img-responsive" src="http://propeller.in/assets/images/profile-pic.png">
    </div>
    
    <div class="pmd-card-title">
        <h2 class="pmd-card-title-text">Title goes here</h2>
        <span class="pmd-card-subtitle-text">Secondary text</span>	
    </div>
    
    <!-- Card body -->	
    <div class="pmd-card-body">
        Cards provide context and an entry point to more robust information and views. Don't overload cards with extraneous information or actions.
    </div>
    
    <!-- Card action -->
    <div class="pmd-card-actions">
        <button type="button" class="btn btn-sm pmd-btn-fab pmd-btn-flat pmd-ripple-effect btn-primary"><i class="material-icons pmd-sm">share</i></button>
        <button type="button" class="btn btn-sm pmd-btn-fab pmd-btn-flat pmd-ripple-effect btn-primary"><i class="material-icons pmd-sm">thumb_up</i></button>
        <button type="button" class="btn btn-sm pmd-btn-fab pmd-btn-flat pmd-ripple-effect btn-primary"><i class="material-icons pmd-sm">drafts</i></button>
    </div>
</div>
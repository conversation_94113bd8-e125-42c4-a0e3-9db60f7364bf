--
-- DataTables Ajax and server-side processing database (Postgres)
--
DROP TABLE IF EXISTS datatables_demo;

CREATE TABLE datatables_demo (
	id         serial,
	first_name text NOT NULL default '',
	last_name  text NOT NULL default '',
	position   text NOT NULL default '',
	email      text NOT NULL default '',
	office     text NOT NULL default '',
	start_date timestamp without time zone default CURRENT_TIMESTAMP,
	age        integer,
	salary     integer,
	seq        integer,
	extn       text NOT NULL default '',
	PRIMARY KEY (id)
);

INSERT INTO datatables_demo
		( id, first_name, last_name, age, position, salary, start_date, extn, email, office, seq ) 
	VALUES
		( 1, '<PERSON>', '<PERSON>', 61, 'System Architect', 320800, '2011/04/25', 5421, '<EMAIL>', 'Edinburgh', 2 ),
		( 2, '<PERSON>', '<PERSON>', 63, 'Accountant', 170750, '2011/07/25', 8422, '<EMAIL>', 'Tokyo', 22 ),
		( 3, '<PERSON>', '<PERSON>', 66, 'Junior Technical Author', 86000, '2009/01/12', 1562, '<EMAIL>', 'San Francisco', 6 ),
		( 4, '<PERSON><PERSON>', '<PERSON>', 22, 'Senior Javascript Developer', 433060, '2012/03/29', 6224, '<EMAIL>', 'Edinburgh', 41 ),
		( 5, 'Airi', '<PERSON>u', 33, 'Accountant', 162700, '2008/11/28', 5407, '<EMAIL>', 'Tokyo', 55 ),
		( 6, 'Brielle', 'Williamson', 61, 'Integration Specialist', 372000, '2012/12/02', 4804, '<EMAIL>', 'New York', 21 ),
		( 7, 'Herrod', 'Chandler', 59, 'Sales Assistant', 137500, '2012/08/06', 9608, '<EMAIL>', 'San Francisco', 46 ),
		( 8, 'Rhona', 'Davidson', 55, 'Integration Specialist', 327900, '2010/10/14', 6200, '<EMAIL>', 'Tokyo', 50 ),
		( 9, 'Colleen', 'Hurst', 39, 'Javascript Developer', 205500, '2009/09/15', 2360, '<EMAIL>', 'San Francisco', 26 ),
		( 10, 'Sonya', 'Frost', 23, 'Software Engineer', 103600, '2008/12/13', 1667, '<EMAIL>', 'Edinburgh', 18 ),
		( 11, 'Jena', 'Gaines', 30, 'Office Manager', 90560, '2008/12/19', 3814, '<EMAIL>', 'London', 13 ),
		( 12, 'Quinn', 'Flynn', 22, 'Support Lead', 342000, '2013/03/03', 9497, '<EMAIL>', 'Edinburgh', 23 ),
		( 13, 'Charde', 'Marshall', 36, 'Regional Director', 470600, '2008/10/16', 6741, '<EMAIL>', 'San Francisco', 14 ),
		( 14, 'Haley', 'Kennedy', 43, 'Senior Marketing Designer', 313500, '2012/12/18', 3597, '<EMAIL>', 'London', 12 ),
		( 15, 'Tatyana', 'Fitzpatrick', 19, 'Regional Director', 385750, '2010/03/17', 1965, '<EMAIL>', 'London', 54 ),
		( 16, 'Michael', 'Silva', 66, 'Marketing Designer', 198500, '2012/11/27', 1581, '<EMAIL>', 'London', 37 ),
		( 17, 'Paul', 'Byrd', 64, 'Chief Financial Officer (CFO)', 725000, '2010/06/09', 3059, '<EMAIL>', 'New York', 32 ),
		( 18, 'Gloria', 'Little', 59, 'Systems Administrator', 237500, '2009/04/10', 1721, '<EMAIL>', 'New York', 35 ),
		( 19, 'Bradley', 'Greer', 41, 'Software Engineer', 132000, '2012/10/13', 2558, '<EMAIL>', 'London', 48 ),
		( 20, 'Dai', 'Rios', 35, 'Personnel Lead', 217500, '2012/09/26', 2290, '<EMAIL>', 'Edinburgh', 45 ),
		( 21, 'Jenette', 'Caldwell', 30, 'Development Lead', 345000, '2011/09/03', 1937, '<EMAIL>', 'New York', 17 ),
		( 22, 'Yuri', 'Berry', 40, 'Chief Marketing Officer (CMO)', 675000, '2009/06/25', 6154, '<EMAIL>', 'New York', 57 ),
		( 23, 'Caesar', 'Vance', 21, 'Pre-Sales Support', 106450, '2011/12/12', 8330, '<EMAIL>', 'New York', 29 ),
		( 24, 'Doris', 'Wilder', 23, 'Sales Assistant', 85600, '2010/09/20', 3023, '<EMAIL>', 'Sidney', 56 ),
		( 25, 'Angelica', 'Ramos', 47, 'Chief Executive Officer (CEO)', 1200000, '2009/10/09', 5797, '<EMAIL>', 'London', 36 ),
		( 26, 'Gavin', 'Joyce', 42, 'Developer', 92575, '2010/12/22', 8822, '<EMAIL>', 'Edinburgh', 5 ),
		( 27, 'Jennifer', 'Chang', 28, 'Regional Director', 357650, '2010/11/14', 9239, '<EMAIL>', 'Singapore', 51 ),
		( 28, 'Brenden', 'Wagner', 28, 'Software Engineer', 206850, '2011/06/07', 1314, '<EMAIL>', 'San Francisco', 20 ),
		( 29, 'Fiona', 'Green', 48, 'Chief Operating Officer (COO)', 850000, '2010/03/11', 2947, '<EMAIL>', 'San Francisco', 7 ),
		( 30, 'Shou', 'Itou', 20, 'Regional Marketing', 163000, '2011/08/14', 8899, '<EMAIL>', 'Tokyo', 1 ),
		( 31, 'Michelle', 'House', 37, 'Integration Specialist', 95400, '2011/06/02', 2769, '<EMAIL>', 'Sidney', 39 ),
		( 32, 'Suki', 'Burks', 53, 'Developer', 114500, '2009/10/22', 6832, '<EMAIL>', 'London', 40 ),
		( 33, 'Prescott', 'Bartlett', 27, 'Technical Author', 145000, '2011/05/07', 3606, '<EMAIL>', 'London', 47 ),
		( 34, 'Gavin', 'Cortez', 22, 'Team Leader', 235500, '2008/10/26', 2860, '<EMAIL>', 'San Francisco', 52 ),
		( 35, 'Martena', 'Mccray', 46, 'Post-Sales support', 324050, '2011/03/09', 8240, '<EMAIL>', 'Edinburgh', 8 ),
		( 36, 'Unity', 'Butler', 47, 'Marketing Designer', 85675, '2009/12/09', 5384, '<EMAIL>', 'San Francisco', 24 ),
		( 37, 'Howard', 'Hatfield', 51, 'Office Manager', 164500, '2008/12/16', 7031, '<EMAIL>', 'San Francisco', 38 ),
		( 38, 'Hope', 'Fuentes', 41, 'Secretary', 109850, '2010/02/12', 6318, '<EMAIL>', 'San Francisco', 53 ),
		( 39, 'Vivian', 'Harrell', 62, 'Financial Controller', 452500, '2009/02/14', 9422, '<EMAIL>', 'San Francisco', 30 ),
		( 40, 'Timothy', 'Mooney', 37, 'Office Manager', 136200, '2008/12/11', 7580, '<EMAIL>', 'London', 28 ),
		( 41, 'Jackson', 'Bradshaw', 65, 'Director', 645750, '2008/09/26', 1042, '<EMAIL>', 'New York', 34 ),
		( 42, 'Olivia', 'Liang', 64, 'Support Engineer', 234500, '2011/02/03', 2120, '<EMAIL>', 'Singapore', 4 ),
		( 43, 'Bruno', 'Nash', 38, 'Software Engineer', 163500, '2011/05/03', 6222, '<EMAIL>', 'London', 3 ),
		( 44, 'Sakura', 'Yamamoto', 37, 'Support Engineer', 139575, '2009/08/19', 9383, '<EMAIL>', 'Tokyo', 31 ),
		( 45, 'Thor', 'Walton', 61, 'Developer', 98540, '2013/08/11', 8327, '<EMAIL>', 'New York', 11 ),
		( 46, 'Finn', 'Camacho', 47, 'Support Engineer', 87500, '2009/07/07', 2927, '<EMAIL>', 'San Francisco', 10 ),
		( 47, 'Serge', 'Baldwin', 64, 'Data Coordinator', 138575, '2012/04/09', 8352, '<EMAIL>', 'Singapore', 44 ),
		( 48, 'Zenaida', 'Frank', 63, 'Software Engineer', 125250, '2010/01/04', 7439, '<EMAIL>', 'New York', 42 ),
		( 49, 'Zorita', 'Serrano', 56, 'Software Engineer', 115000, '2012/06/01', 4389, '<EMAIL>', 'San Francisco', 27 ),
		( 50, 'Jennifer', 'Acosta', 43, 'Junior Javascript Developer', 75650, '2013/02/01', 3431, '<EMAIL>', 'Edinburgh', 49 ),
		( 51, 'Cara', 'Stevens', 46, 'Sales Assistant', 145600, '2011/12/06', 3990, '<EMAIL>', 'New York', 15 ),
		( 52, 'Hermione', 'Butler', 47, 'Regional Director', 356250, '2011/03/21', 1016, '<EMAIL>', 'London', 9 ),
		( 53, 'Lael', 'Greer', 21, 'Systems Administrator', 103500, '2009/02/27', 6733, '<EMAIL>', 'London', 25 ),
		( 54, 'Jonas', 'Alexander', 30, 'Developer', 86500, '2010/07/14', 8196, '<EMAIL>', 'San Francisco', 33 ),
		( 55, 'Shad', 'Decker', 51, 'Regional Director', 183000, '2008/11/13', 6373, '<EMAIL>', 'Edinburgh', 43 ),
		( 56, 'Michael', 'Bruce', 29, 'Javascript Developer', 183000, '2011/06/27', 5384, '<EMAIL>', 'Singapore', 16 ),
		( 57, 'Donna', 'Snider', 27, 'Customer Support', 112000, '2011/01/25', 4226, '<EMAIL>', 'New York', 19 );

ALTER SEQUENCE datatables_demo_id_seq RESTART WITH 58;
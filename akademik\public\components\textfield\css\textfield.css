/*!
 * Propeller v1.0.0 (http://propeller.in)
 * Copyright 2016-2017 Digicorp, Inc.
 * Licensed under MIT (http://propeller.in/LICENSE)
 */

/* Propeller css for text fields */
.pmd-textfield-focused{ transition-duration: 0.2s;  -moz-transition-duration: 0.2s;  -ms-transition-duration: 0.2s;  -o-transition-duration: 0.2s;  -webkit-transition-duration: 0.2s; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); -moz-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); -ms-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); -o-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/*input focus bar*/
.pmd-textfield-focused{ display: block;  width: 100%; height:2px; background-color:#e2e2e2; display:block; top:-1px;  background-color:#4285f4; transform:scaleX(0); -moz-transform: scaleX(0); -ms-transform: scaleX(0); -o-transform: scaleX(0);  -webkit-transform: scaleX(0); position:relative; z-index:2;}
.pmd-textfield.pmd-textfield-floating-label-active .pmd-textfield-focused { transform: scaleX(1); -moz-transform: scaleX(1); -ms-transform: scaleX(1); -o-transform: scaleX(1); -webkit-transform: scaleX(1);}

/*paper input*/
.form-group.pmd-textfield{ margin-bottom:16px; line-height:22px;}
.pmd-textfield .form-control{background: transparent; border: none; border-bottom:solid 1px #e6e6e6; outline: none; box-shadow:none; padding:0; border-radius:0; font-size:16px;}
.pmd-textfield .form-control{padding-bottom:6px;}
.pmd-textfield input.form-control{height: inherit;}
.pmd-textfield textarea.form-control{height: 80px;}
.pmd-textfield label{font-weight:normal; line-height:1.4; font-size:14px; color:rgba(0, 0, 0, 0.4); margin-bottom: 0;}

/*paper input group*/
.pmd-input-group-label{ padding-left:40px;}
.pmd-textfield-floating-label.pmd-textfield-floating-label-completed label.pmd-input-group-label{ font-size:14px; transform: translateY(0px);  -moz-transform: translateY(0px); -ms-transform: translateY(0px); -o-transform: translateY(0px); -webkit-transform: translateY(0px);}
.pmd-textfield .input-group-addon{ border:none; background-color: transparent;}
.pmd-textfield .input-group .form-control{ float:inherit; z-index:inherit;}
.pmd-textfield .input-group .input-group-addon{ padding:0;}
.pmd-textfield .input-group .input-group-addon:first-child{ padding-right:16px;}
.pmd-textfield .input-group .input-group-addon:last-child{ padding-left:16px;}

/*floating label*/
.pmd-textfield-floating-label { position: relative;}
.pmd-textfield-floating-label label { transform: translateY(26px); -moz-transform:translateY(26px); -webkit-transform:translateY(26px); -ms-transform:translateY(26px); -o-transform:translateY(26px); margin:0; font-size: 16px; line-height:24px; transition-duration: 0.2s; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); margin-bottom:4px;}
.pmd-textfield-floating-label .form-control{position: relative;}
.pmd-textfield-floating-label.pmd-textfield-floating-label-completed label {  transform: translateY(0px);  -moz-transform: translateY(0px);  -ms-transform: translateY(0px);  -o-transform: translateY(0px);  -webkit-transform: translateY(0px);  color:rgba(0, 0, 0, 0.54); font-size:14px;}

/*paper input error*/
.pmd-textfield.has-success .form-control:focus,
.pmd-textfield.has-warning .form-control:focus,
.pmd-textfield.has-error .form-control:focus{box-shadow:none;}
.has-error-text{ display:none;}

/*has error*/
.pmd-textfield.has-error .form-control{color:#a94442; border-color:#a94442;}
.pmd-textfield.has-error .form-control ~ .pmd-textfield-focused{ background-color:#a94442;}
.pmd-textfield.has-error .form-control ~ .has-error-text{ color:#a94442; display:block;}
.pmd-textfield.has-error .form-control:invalid{color:#a94442;}
.pmd-textfield.has-error .form-control:invalid ~ .pmd-textfield-focused{ background-color:#a94442;}
.pmd-textfield.has-error .form-control:invalid ~ .has-error-text{ color:#a94442; display:block;}
.pmd-textfield-floating-label.pmd-textfield-floating-label-completed.has-error label{color:#a94442;}

/*has success*/
.pmd-textfield.has-success .form-control{color:#3c763d; border-color:#3c763d;}
.pmd-textfield.has-success .form-control ~ .pmd-textfield-focused{ background-color:#3c763d;}
.pmd-textfield.has-success .form-control ~ .has-error-text{ color:#3c763d; display:block;}
.pmd-textfield-floating-label.pmd-textfield-floating-label-completed.has-success label{color:#3c763d;}

/*has warning*/
.pmd-textfield.has-warning .form-control{color:#8a6d3b; border-color:#8a6d3b;}
.pmd-textfield.has-warning .form-control ~ .pmd-textfield-focused{ background-color:#8a6d3b;}
.pmd-textfield.has-warning .form-control ~ .has-error-text{ color:#8a6d3b; display:block;}
.pmd-textfield-floating-label.pmd-textfield-floating-label-completed.has-warning label{color:#8a6d3b;}

/*help block*/
.help-block{ font-size:14px; margin-top:0;}

/* Large fields size */
.form-group-lg.pmd-textfield .form-control{ font-size: 20px; height: 44px; line-height: 1.33333;}
.form-group-lg.pmd-textfield label{font-size: 16px;}
.form-group-lg.pmd-textfield-floating-label label{font-size: 20px; transform: translateY(36px); -moz-transform:translateY(36px); -webkit-transform:translateY(36px); -ms-transform:translateY(36px); -o-transform:translateY(36px);}
.form-group-lg.pmd-textfield-floating-label.pmd-textfield-floating-label-completed label{font-size: 16px; transform: translateY(0); -moz-transform:translateY(0); -webkit-transform:translateY(0); -ms-transform:translateY(0); -o-transform:translateY(0);}

/* Small fields size */
.form-group-sm.pmd-textfield .form-control{ font-size: 14px; height: 30px; line-height: 1.33333;}
.form-group-sm.pmd-textfield label{font-size: 10px;}
.form-group-sm.pmd-textfield-floating-label label{font-size: 14px; transform: translateY(28px); -moz-transform:translateY(28px); -webkit-transform:translateY(28px); -ms-transform:translateY(28px); -o-transform:translateY(28px);}
.form-group-sm.pmd-textfield-floating-label.pmd-textfield-floating-label-completed label{font-size: 10px; transform: translateY(0); -moz-transform:translateY(0); -webkit-transform:translateY(0); -ms-transform:translateY(0); -o-transform:translateY(0);}
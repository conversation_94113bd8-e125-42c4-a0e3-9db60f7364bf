<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>Select examples - Select for DataTables</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Select example <span>Select for DataTables</span></h1>

			<div class="info">
				<p>Select adds item selection capabilities to a DataTable. Items can be rows, columns or cells, which can be selected independently, or together. Item selection
				can be particularly useful in interactive tables where users can perform some action on the table such as editing.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./initialisation/index.html">Initialisation</a></h3>
						<ul class="toc">
							<li><a href="./initialisation/simple.html">Simple initialisation</a></li>
							<li><a href="./initialisation/single.html">Single item selection</a></li>
							<li><a href="./initialisation/multi.html">Multi item selection</a></li>
							<li><a href="./initialisation/cells.html">Cell selection</a></li>
							<li><a href="./initialisation/checkbox.html">Checkbox selection</a></li>
							<li><a href="./initialisation/i18n.html">Internationalisation</a></li>
							<li><a href="./initialisation/blurable.html">Blur selection</a></li>
							<li><a href="./initialisation/deferRender.html">Defer rendering</a></li>
							<li><a href="./initialisation/buttons.html">Buttons</a></li>
							<li><a href="./initialisation/reload.html">Retain selection on reload</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="./styling/bootstrap.html">Bootstrap styling</a></li>
							<li><a href="./styling/foundation.html">Foundation styling</a></li>
							<li><a href="./styling/jqueryui.html">jQuery UI styling</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./api/index.html">API</a></h3>
						<ul class="toc">
							<li><a href="./api/events.html">Events</a></li>
							<li><a href="./api/get.html">Get selected items</a></li>
							<li><a href="./api/select.html">Select items</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
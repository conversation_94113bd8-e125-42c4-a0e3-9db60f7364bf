/*!
 AutoFill 2.0.0
 ©2008-2015 SpryMedia Ltd - datatables.net/license
*/
(function(o,i,p){var n=function(f,j){var n=0,g=function(c,b){if(!j.versionCheck||!j.versionCheck("1.10.8"))throw"Warning: AutoFill requires DataTables 1.10.8 or greater";this.c=f.extend(!0,{},j.defaults.autoFill,g.defaults,b);this.s={dt:new j.Api(c),namespace:".autoFill"+n++,scroll:{},scrollInterval:null};this.dom={handle:f('<div class="dt-autofill-handle"/>'),select:{top:f('<div class="dt-autofill-select top"/>'),right:f('<div class="dt-autofill-select right"/>'),bottom:f('<div class="dt-autofill-select bottom"/>'),
left:f('<div class="dt-autofill-select left"/>')},background:f('<div class="dt-autofill-background"/>'),list:f('<div class="dt-autofill-list">'+this.s.dt.i18n("autoFill.info","")+"<ul/></div>"),dtScroll:null};this._constructor()};g.prototype={_constructor:function(){var c=this,b=this.s.dt,a=f("div.dataTables_scrollBody",this.s.dt.table().container());a.length&&(this.dom.dtScroll=a,"static"===a.css("position")&&a.css("position","relative"));this._focusListener();this.dom.handle.on("mousedown",function(a){c._mousedown(a);
return false});b.on("destroy.autoFill",function(){b.off(".autoFill");f(b.table().body()).off(c.s.namespace);f(i.body).off(c.s.namespace)})},_attach:function(c){var b=this.s.dt,a=b.cell(c).index();!a||-1===b.columns(this.c.columns).indexes().indexOf(a.column)?this._detach():(this.dom.attachedTo=c,this.dom.handle.appendTo(c))},_actionSelector:function(c){var b=this,a=this.s.dt,d=g.actions,e=[];f.each(d,function(b,d){d.available(a,c)&&e.push(b)});if(1===e.length&&!1===this.c.alwaysAsk){var h=d[e[0]].execute(a,
c);this._update(h,c)}else{var k=this.dom.list.children("ul").empty();e.push("cancel");f.each(e,function(e,h){k.append(f("<li/>").append('<div class="dt-autofill-question">'+d[h].option(a,c)+"<div>").append(f('<div class="dt-autofill-button">').append(f('<button class="'+g.classes.btn+'">'+a.i18n("autoFill.button","&gt;")+"</button>").on("click",function(){var e=d[h].execute(a,c,f(this).closest("li"));b._update(e,c);b.dom.background.remove();b.dom.list.remove()}))))});this.dom.background.appendTo("body");
this.dom.list.appendTo("body");this.dom.list.css("margin-top",-1*(this.dom.list.outerHeight()/2))}},_detach:function(){this.dom.attachedTo=null;this.dom.handle.detach()},_drawSelection:function(c){var b=this.s.dt,a=this.s.start,d=f(this.dom.start),e=f(c),h={row:e.parent().index(),column:e.index()};if(b.cell(e).any()&&-1!==b.columns(this.c.columns).indexes().indexOf(h.column)){this.s.end=h;var k,b=a.row<h.row?d:e;k=a.row<h.row?e:d;c=a.column<h.column?d:e;d=a.column<h.column?e:d;b=b.position().top;
c=c.position().left;a=k.position().top+k.outerHeight()-b;d=d.position().left+d.outerWidth()-c;if(e=this.dom.dtScroll)b+=e.scrollTop(),c+=e.scrollLeft();e=this.dom.select;e.top.css({top:b,left:c,width:d});e.left.css({top:b,left:c,height:a});e.bottom.css({top:b+a,left:c,width:d});e.right.css({top:b,left:c+d,height:a})}},_editor:function(c){var b=this.s.dt,a=this.c.editor;if(a){for(var d={},e=[],h=0,f=c.length;h<f;h++)for(var i=0,g=c[h].length;i<g;i++){var l=c[h][i],m=b.settings()[0].aoColumns[l.index.column],
m=m.editField!==p?m.editField:m.mData;if(!m)throw"Could not automatically determine field name. Please see https://datatables.net/tn/11";d[m]||(d[m]={});var j=b.row(l.index.row).id();d[m][j]=l.set;e.push(l.index)}a.bubble(e,!1).multiSet(d).submit()}},_emitEvent:function(c,b){this.s.dt.iterator("table",function(a){f(a.nTable).triggerHandler(c+".dt",b)})},_focusListener:function(){var c=this,b=this.s.dt,a=this.s.namespace,d=null!==this.c.focus?this.c.focus:b.settings()[0].keytable?"focus":"hover";if("focus"===
d)b.on("key-focus.autoFill",function(a,b,d){c._attach(d.node())}).on("key-blur.autoFill",function(){c._detach()});else if("click"===d)f(b.table().body()).on("click"+a,"td, th",function(){c._attach(this)}),f(i.body).on("click"+a,function(a){f(a.target).parents().filter(b.table().body()).length||c._detach()});else f(b.table().body()).on("mouseenter"+a,"td, th",function(){c._attach(this)}).on("mouseleave"+a,function(){c._detach()})},_mousedown:function(c){var b=this;this.dom.start=this.dom.attachedTo;
this.s.start={row:f(this.dom.start).parent().index(),column:f(this.dom.start).index()};f(i.body).on("mousemove.autoFill",function(a){b._mousemove(a)}).on("mouseup.autoFill",function(a){b._mouseup(a)});var a=this.dom.select,d=f(this.s.dt.table().body()).offsetParent();a.top.appendTo(d);a.left.appendTo(d);a.right.appendTo(d);a.bottom.appendTo(d);this._drawSelection(this.dom.start,c);this.dom.handle.css("display","none");c=this.dom.dtScroll;this.s.scroll={windowHeight:f(o).height(),windowWidth:f(o).width(),
dtTop:c?c.offset().top:null,dtLeft:c?c.offset().left:null,dtHeight:c?c.outerHeight():null,dtWidth:c?c.outerWidth():null}},_mousemove:function(c){var b=c.target.nodeName.toLowerCase();"td"!==b&&"th"!==b||(this._drawSelection(c.target,c),this._shiftScroll(c))},_mouseup:function(){f(i.body).off(".autoFill");var c=this.s.dt,b=this.dom.select;b.top.remove();b.left.remove();b.right.remove();b.bottom.remove();this.dom.handle.css("display","block");var b=this.s.start,a=this.s.end;if(!(b.row===a.row&&b.column===
a.column)){for(var d=this._range(b.row,a.row),b=this._range(b.column,a.column),a=[],e=0;e<d.length;e++)a.push(f.map(b,function(a){a=c.cell(":eq("+d[e]+")",a+":visible",{page:"current"});return{cell:a,data:a.data(),index:a.index()}}));this._actionSelector(a)}},_range:function(c,b){var a=[],d;if(c<=b)for(d=c;d<=b;d++)a.push(d);else for(d=c;d>=b;d--)a.push(d);return a},_shiftScroll:function(c){var b=this,a=this.s.scroll,d=!1,e=c.pageY-i.body.scrollTop,f=c.pageX-i.body.scrollLeft,k,g,j,l;65>e?k=-5:e>
a.windowHeight-65&&(k=5);65>f?g=-5:f>a.windowWidth-65&&(g=5);null!==a.dtTop&&c.pageY<a.dtTop+65?j=-5:null!==a.dtTop&&c.pageY>a.dtTop+a.dtHeight-65&&(j=5);null!==a.dtLeft&&c.pageX<a.dtLeft+65?l=-5:null!==a.dtLeft&&c.pageX>a.dtLeft+a.dtWidth-65&&(l=5);k||g||j||l?(a.windowVert=k,a.windowHoriz=g,a.dtVert=j,a.dtHoriz=l,d=!0):this.s.scrollInterval&&(clearInterval(this.s.scrollInterval),this.s.scrollInterval=null);!this.s.scrollInterval&&d&&(this.s.scrollInterval=setInterval(function(){if(a.windowVert)i.body.scrollTop=
i.body.scrollTop+a.windowVert;if(a.windowHoriz)i.body.scrollLeft=i.body.scrollLeft+a.windowHoriz;if(a.dtVert||a.dtHoriz){var c=b.dom.dtScroll[0];if(a.dtVert)c.scrollTop=c.scrollTop+a.dtVert;if(a.dtHoriz)c.scrollLeft=c.scrollLeft+a.dtHoriz}},20))},_update:function(c,b){if(!1!==c){var a=this.s.dt,d;this._emitEvent("preAutoFill",[a,b]);this._editor(b);if(null!==this.c.update?this.c.update:!this.c.editor){for(var e=0,f=b.length;e<f;e++)for(var g=0,i=b[e].length;g<i;g++)d=b[e][g],d.cell.data(d.set);a.draw(!1)}this._emitEvent("autoFill",
[a,b])}}};g.actions={increment:{available:function(c,b){return f.isNumeric(b[0][0].data)},option:function(c){return c.i18n("autoFill.increment",'Increment / decrement each cell by: <input type="number" value="1">')},execute:function(c,b,a){for(var c=1*b[0][0].data,a=1*f("input",a).val(),d=0,e=b.length;d<e;d++)for(var h=0,g=b[d].length;h<g;h++)b[d][h].set=c,c+=a}},fill:{available:function(){return!0},option:function(c,b){return c.i18n("autoFill.fill","Fill all cells with <i>"+b[0][0].data+"</i>")},
execute:function(c,b){for(var a=b[0][0].data,d=0,e=b.length;d<e;d++)for(var f=0,g=b[d].length;f<g;f++)b[d][f].set=a}},fillHorizontal:{available:function(c,b){return 1<b.length&&1<b[0].length},option:function(c){return c.i18n("autoFill.fillHorizontal","Fill cells horizontally")},execute:function(c,b){for(var a=0,d=b.length;a<d;a++)for(var e=0,f=b[a].length;e<f;e++)b[a][e].set=b[a][0].data}},fillVertical:{available:function(c,b){return 1<b.length&&1<b[0].length},option:function(c){return c.i18n("autoFill.fillVertical",
"Fill cells vertically")},execute:function(c,b){for(var a=0,d=b.length;a<d;a++)for(var e=0,f=b[a].length;e<f;e++)b[a][e].set=b[0][e].data}},cancel:{available:function(){return!1},option:function(c){return c.i18n("autoFill.cancel","Cancel")},execute:function(){return!1}}};g.version="2.0.0";g.defaults={alwaysAsk:!1,focus:null,columns:"",update:null,editor:null};g.classes={btn:"btn"};f(i).on("init.dt.autofill",function(c,b){if("dt"===c.namespace){var a=b.oInit.autoFill,d=j.defaults.autoFill;if(a||d)d=
f.extend({},a,d),!1!==a&&new g(b,d)}});j.AutoFill=g;return j.AutoFill=g};"function"===typeof define&&define.amd?define(["jquery","datatables"],n):"object"===typeof exports?n(require("jquery"),require("datatables")):jQuery&&!jQuery.fn.dataTable.AutoFill&&n(jQuery,jQuery.fn.dataTable)})(window,document);

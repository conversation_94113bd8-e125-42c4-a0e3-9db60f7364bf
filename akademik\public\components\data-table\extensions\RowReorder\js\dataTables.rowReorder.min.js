/*!
 RowReorder 1.0.0
 2015 SpryMedia Ltd - datatables.net/license
*/
(function(l,i){var k=function(e,h){var g=function(b,a){if(!h.versionCheck||!h.versionCheck("1.10.8"))throw"DataTables RowReorder requires DataTables 1.10.8 or newer";this.c=e.extend(!0,{},h.defaults.rowReorder,g.defaults,a);this.s={bodyTop:null,dt:new h.Api(b),getDataFn:h.ext.oApi._fnGetObjectDataFn(this.c.dataSrc),middles:null,setDataFn:h.ext.oApi._fnSetObjectDataFn(this.c.dataSrc),start:{top:0,left:0,offsetTop:0,offsetLeft:0,nodes:[]},windowHeight:0};this.dom={clone:null};var d=this.s.dt.settings()[0],
c=d.rowreorder;if(c)return c;d.rowreorder=this;this._constructor()};g.prototype={_constructor:function(){var b=this,a=this.s.dt,d=e(a.table().node());"static"===d.css("position")&&d.css("position","relative");e(d).on("mousedown.rowReorder touchstart.rowReorder",this.c.selector,function(c){var d=e(this).closest("tr");if(a.row(d).any())return b._mouseDown(c,d),!1});a.on("destroy",function(){d.off("mousedown.rowReorder")})},_cachePositions:function(){var b=this.s.dt,a=e(b.table().node()).find("thead").outerHeight(),
d=e.unique(b.rows({page:"current"}).nodes().toArray()),c=e.map(d,function(b){return e(b).position().top-a}),d=e.map(c,function(a,d){return c.length<d-1?(a+c[d+1])/2:(a+a+e(b.row(":last-child").node()).outerHeight())/2});this.s.middles=d;this.s.bodyTop=e(b.table().body()).offset().top;this.s.windowHeight=e(l).height()},_clone:function(b){var a=e(this.s.dt.table().node().cloneNode(!1)).addClass("dt-rowReorder-float").append("<tbody/>").append(b.clone(!1)),d=b.outerWidth(),c=b.outerHeight(),f=b.children().map(function(){return e(this).width()});
a.width(d).height(c).find("tr").children().each(function(a){this.style.width=f[a]+"px"});a.appendTo("body");this.dom.clone=a},_clonePosition:function(b){var a=this.s.start,d=this._eventToPage(b,"Y")-a.top,b=this._eventToPage(b,"X")-a.left,c=this.c.snapX;this.dom.clone.css({top:d+a.offsetTop,left:!0===c?a.offsetLeft:"number"===typeof c?a.offsetLeft+c:b+a.offsetLeft})},_emitEvent:function(b,a){this.s.dt.iterator("table",function(d){e(d.nTable).triggerHandler(b+".dt",a)})},_eventToPage:function(b,a){return-1!==
b.type.indexOf("touch")?b.originalEvent.touches[0]["page"+a]:b["page"+a]},_mouseDown:function(b,a){var d=this,c=this.s.dt,f=this.s.start,g=a.offset();f.top=this._eventToPage(b,"Y");f.left=this._eventToPage(b,"X");f.offsetTop=g.top;f.offsetLeft=g.left;f.nodes=e.unique(c.rows({page:"current"}).nodes().toArray());this._cachePositions();this._clone(a);this._clonePosition(b);this.dom.target=a;a.addClass("dt-rowReorder-moving");e(i).on("mouseup.rowReorder touchend.rowReorder",function(a){d._mouseUp(a)}).on("mousemove.rowReorder touchmove.rowReorder",
function(a){d._mouseMove(a)});e(l).width()===e(i).width()&&e(i.body).addClass("dt-rowReorder-noOverflow")},_mouseMove:function(b){this._clonePosition(b);for(var a=this._eventToPage(b,"Y")-this.s.bodyTop,d=this.s.middles,c=null,f=this.s.dt,g=f.table().body(),j=0,h=d.length;j<h;j++)if(a<d[j]){c=j;break}null===c&&(c=d.length);if(null===this.s.lastInsert||this.s.lastInsert!==c)0===c?this.dom.target.prependTo(g):(a=e.unique(f.rows({page:"current"}).nodes().toArray()),c>this.s.lastInsert?this.dom.target.before(a[c-
1]):this.dom.target.after(a[c])),this._cachePositions(),this.s.lastInsert=c;b=this._eventToPage(b,"Y")-i.body.scrollTop;c=this.s.scrollInterval;65>b?c||(this.s.scrollInterval=setInterval(function(){i.body.scrollTop=i.body.scrollTop-5},15)):65>this.s.windowHeight-b?c||(this.s.scrollInterval=setInterval(function(){i.body.scrollTop=i.body.scrollTop+5},15)):(clearInterval(c),this.s.scrollInterval=null)},_mouseUp:function(){var b=this.s.dt,a,d;this.dom.clone.remove();this.dom.clone=null;this.dom.target.removeClass("dt-rowReorder-moving");
e(i).off(".rowReorder");e(i.body).removeClass("dt-rowReorder-noOverflow");var c=this.s.start.nodes,f=e.unique(b.rows({page:"current"}).nodes().toArray()),g={},j=[],h=[],k=this.s.getDataFn,l=this.s.setDataFn;a=0;for(d=c.length;a<d;a++)if(c[a]!==f[a]){var m=b.row(f[a]).id(),o=b.row(f[a]).data(),n=b.row(c[a]).data();m&&(g[m]=k(n));j.push({node:f[a],oldData:k(o),newData:k(n),newPosition:a,oldPosition:e.inArray(f[a],c)});h.push(f[a])}this._emitEvent("row-reorder",[j,{dataSrc:this.c.dataSrc,nodes:h,values:g}]);
this.c.editor&&this.c.editor.edit(h,!1,{submit:"changed"}).multiSet(this.c.dataSrc,g).submit();if(this.c.update){a=0;for(d=j.length;a<d;a++)c=b.row(j[a].node),f=c.data(),l(f,j[a].newData),c.invalidate("data");b.draw(!1)}}};g.defaults={dataSrc:0,editor:null,selector:"td:first-child",snapX:!1,update:!0};g.version="1.0.0";e.fn.dataTable.RowReorder=g;e.fn.DataTable.RowReorder=g;e(i).on("init.dt.dtr",function(b,a){if("dt"===b.namespace){var d=a.oInit.rowReorder,c=h.defaults.rowReorder;if(d||c)c=e.extend({},
d,c),!1!==d&&new g(a,c)}});return g};"function"===typeof define&&define.amd?define(["jquery","datatables"],k):"object"===typeof exports?k(require("jquery"),require("datatables")):jQuery&&!jQuery.fn.dataTable.RowReorder&&k(jQuery,jQuery.fn.dataTable)})(window,document);

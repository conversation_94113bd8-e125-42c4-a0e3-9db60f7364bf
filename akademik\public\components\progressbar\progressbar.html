<!doctype html>
<html lang="">
<head>
 	<meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="Propeller is a front-end responsive framework based on Material design & Bootstrap.">
	<meta content="width=device-width, initial-scale=1, user-scalable=no" name="viewport">
    <title>Progressbar - Propeller Components</title>
    
	<!-- favicon --> 
	<link rel="icon" href="http://propeller.in/assets/images/favicon.ico" type="image/x-icon">
	
	<!-- Bootstrap --> 
	<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" type="text/css" rel="stylesheet" /> 
	
	<!-- Propeller card (CSS for helping component example file)-->
	<link href="http://propeller.in/components/card/css/card.css" type="text/css" rel="stylesheet" />
	
	<!-- Example docs (CSS for helping component example file)-->
	<link href="http://propeller.in/docs/css/example-docs.css" type="text/css" rel="stylesheet" />
	
	<!-- Propeller typography (CSS for helping component example file) -->
	<link href="http://propeller.in/components/typography/css/typography.css" type="text/css" rel="stylesheet" />

	<!-- Propeller progressbar -->
	<link href="css/progressbar.css" type="text/css" rel="stylesheet" /> 

</head>
 
<body>

<!-- Progressbar -->
<div class="pmd-content pmd-content-custom" id="content"> 

    <!-- component header-->
    <div class="componant-title-bg">
    	<div class="container">
            <div class="row"> 
              
              <!-- component title and description-->
              <div class="col-xs-12">
			  	<h1>Progressbar</h1>
                <p class="lead">Progressbar provide up-to-date feedback on the progress of a workflow or action with simple yet flexible bars.</p>
              </div><!-- component title and description end-->  
              
            </div>
      	</div> 
    </div><!--end component header-->

	<div class="container">
		
		<!-- Bootstrap Progressbar -->
  		<section class="row component-section">
        
        	<!-- Bootstrap progressbar title and description -->
    		<div class="col-md-3">
      			<div id="boot">
        			<h2>Bootstrap Progressbar</h2>
     			</div>
      			<p>This is the default progress bar provided by Bootstrap. You can also convert it to stripped, animated or stacked progressbar.</p>
    		</div> <!-- end static progressbar title and description -->           
            
            <div class="col-md-9"> 
            
            	<!-- static progressbar code, example -->
              	<div class="component-box">
                    <!-- Static Progress bar example -->
                    <div class="row">
                        <div class="col-md-12"> 
                          	<div class="pmd-card pmd-z-depth pmd-card-custom-view">
                            	<div class="pmd-card-body"> 
                                	<div class="progress-rounded progress">
                                    	<div class="progress-bar progress-bar-success" style="width: 34%;"></div>
                                  	</div>
                              		<div class="progress-rounded progress">
                                		<div class="progress-bar progress-bar-info" style="width: 68%;"></div>
                              		</div>
                              		<div class="progress-rounded progress">
                                		<div class="progress-bar progress-bar-warning" style="width: 52%;"></div>
                              		</div>
                              		<div class="progress-rounded progress">
                                		<div class="progress-bar progress-bar-danger" style="width: 77%;"></div>
                              		</div>
                            	</div>
                          	</div>
                        </div>
                   </div><!-- Static Progress bar example end -->
                   
              	</div><!--Static Progressbar code, example end --> 
            </div>
  		</section><!-- Static Progressbar end -->
  
  		<!-- Static Progressbar -->
  		<section class="row component-section">
        
        	<!-- static progressbar title and description -->
    		<div class="col-md-3">
      			<div id="basic">
        			<h2>Propeller Progressbar</h2>
     			</div>
      			<p>It consists of Bootstrap HTML structure enhanced with Propeller customized classes based on Material Standards. Include Propeller Static Progressbar to your code by adding <code>.pmd-progress</code>.</p>
    		</div> <!-- end static progressbar title and description -->           
            
            <div class="col-md-9"> 
            
            	<!-- static progressbar code, example -->
              	<div class="component-box">
                    <!-- Static Progress bar example -->
                    <div class="row">
                        <div class="col-md-12"> 
                          	<div class="pmd-card pmd-z-depth pmd-card-custom-view">
                            	<div class="pmd-card-body"> 
                                	<div class="progress-rounded progress pmd-progress">
                                    	<div class="progress-bar progress-bar-success" style="width: 34%;"></div>
                                  	</div>
                              		<div class="progress-rounded progress pmd-progress">
                                		<div class="progress-bar progress-bar-info" style="width: 68%;"></div>
                              		</div>
                              		<div class="progress-rounded progress pmd-progress">
                                		<div class="progress-bar progress-bar-warning" style="width: 52%;"></div>
                              		</div>
                              		<div class="progress-rounded progress pmd-progress">
                                		<div class="progress-bar progress-bar-danger" style="width: 77%;"></div>
                              		</div>
                            	</div>
                          	</div>
                        </div>
                   </div><!-- Static Progress bar example end -->
                   
              	</div><!--Static Progressbar code, example end --> 
            </div>
  		</section><!-- Static Progressbar end --> 
  
  		<!-- Striped Progressbar -->
  		<section class="row component-section">
        
    		<!-- striped progressbar title and description -->
            <div class="col-md-3">
      			<div id="striped">
        			<h2>Striped Progressbar</h2>
      			</div>
      			<p>Include Propeller Striped Progressbar to your code by adding <code>.progress-striped</code> besides <code>.pmd-progress</code>.</p>
    		</div><!-- end striped progressbar title and description -->
            
            <div class="col-md-9">
             
              <!-- striped progressbar code, example -->
              <div class="component-box">
                <!-- Striped Progress bar example -->
                <div class="row">
                	<div class="col-md-12"> 
                    	<div class="pmd-card pmd-z-depth pmd-card-custom-view">
                        	<div class="pmd-card-body"> 
                            	<div class=" progress-rounded progress progress-striped pmd-progress">
                                	<div class="progress-bar progress-bar-success" style="width: 34%;"></div>
                              	</div>
                              	<div class=" progress-rounded progress progress-striped pmd-progress">
                                	<div class="progress-bar progress-bar-info" style="width: 68%;"></div>
                              	</div>
                          		<div class=" progress-rounded progress progress-striped pmd-progress">
                            		<div class="progress-bar progress-bar-warning" style="width: 52%;"></div>
                          		</div>
                          		<div class=" progress-rounded progress progress-striped pmd-progress">
                            		<div class="progress-bar progress-bar-danger" style="width: 77%;"></div>
                          		</div>
                        	</div>
                      	</div>
                    </div>
                </div><!-- Striped Progress bar example end -->
                
              </div><!-- striped progressbar code, example end -->
           </div>
  		</section><!-- Striped Progressbar end --> 
  
  		<!-- Animated Progressbar -->
        <section class="row component-section">
        
        	<!-- animated progressbar title and description -->
            <div class="col-md-3">
              <div id="animated">
                <h2>Animated Progressbar</h2>
              </div>
              <p>Include Propeller Animated Progressbar to your code by adding <code>.active</code> beside <code>.progress-striped</code>.</p>
            </div><!-- end animated progressbar title and description -->
            
            <div class="col-md-9">
             
            	<!-- striped progressbar code, example -->
              	<div class="component-box">
                  	<!-- Animated Progress bar example -->
                    <div class="row">
                    	<div class="col-md-12"> 
                      		<div class="pmd-card pmd-z-depth pmd-card-custom-view">
                        		<div class="pmd-card-body"> 
                          			<div class=" progress-rounded progress progress-striped pmd-progress active">
                            			<div class="progress-bar progress-bar-success" style="width: 34%;"></div>
                          			</div>
                          			<div class=" progress-rounded progress progress-striped pmd-progress active">
                            			<div class="progress-bar progress-bar-info" style="width: 68%;"></div>
                          			</div>
                        		</div>
                      		</div>
                    	</div>
                  </div><!-- Animated Progress bar example end -->
                    
              	</div><!-- Animated progressbar code, example end -->
            </div>
        </section><!-- Animated Progressbar --> 
  
  		<!-- Stacked progressbar -->
  		<section class="row component-section">
        
        	<!-- stacked progressbar title and description -->
    		<div class="col-md-3">
      			<div id="stake">
        			<h2>Stacked Progressbar</h2>
      			</div>
      			<p>Include Propeller Stacked Progressbar to your code by adding <code>.progress-bar</code> inside the wrapper div containing <code>.pmd-progress</code>.</p>
    		</div><!-- stacked progressbar title and description -->
            
    		<div class="col-md-9">
             
      			<!--Stacked Progressbar code, example -->
      			<div class="component-box">
          			<!-- Stacked Progress bar example -->
                    <div class="row">
            			<div class="col-md-12"> 
              				<div class="pmd-card pmd-z-depth pmd-card-custom-view ">
                				<div class="pmd-card-body"> 
                                	<div class="progress-rounded progress progress-striped pmd-progress">
                                    	<div style="width: 35%" class="progress-bar progress-bar-success "></div>
                                    	<div style="width: 20%" class="progress-bar progress-bar-warning"></div>
                                    	<div style="width: 10%" class="progress-bar progress-bar-danger"></div>
                                  	</div>
                				</div>
              				</div>
            			</div>
          			</div><!-- Stacked Progress bar example end-->
                       
     			</div><!--Staked Progressbar code,example end --> 
    		</div>
  		</section><!-- Stacked progressbar end-->
   
  		<!-- Configuration starts-->  
  		<section class="row component-section">
        
    		<!-- Configuration title and description -->
            <div class="col-md-3">
      			<div id="config">
        			<h2>Configuration Options</h2>
      			</div>
      			<p>The Propeller CSS classes apply various predefined visual enhancements to the progressbar. The table lists the available classes and their effects.</p>
    		</div><!-- Configuration title and description end -->
            
    		<div class="col-md-9"> 
            
      			<!--Propeller  Class Configuration -->
      			<div class="pmd-card pmd-table-card-responsive">
        			<div class="pmd-table-card">
                    	<table class="table pmd-table table-hover">
                        	<thead>
                          		<tr>
                            		<th>Propeller Class</th>
                            		<th>Effect</th>
                            		<th>Remark</th>
                          		</tr>
                        	</thead>
                        	<tbody>
								<tr>
                            		<td data-title="Class"><code>.progress</code></td>
                            		<td data-title="Effect">Used to initialize static progress bar.</td>
                            		<td data-title="Remark">Required</td>
                          		</tr>
                          		<tr>
                            		<td data-title="Class"><code>.pmd-progress</code></td>
                            		<td data-title="Effect">Add this class to create propeller customized progress bar.</td>
                            		<td data-title="Remark">Required</td>
                          		</tr>
                          		<tr>
                            		<td data-title="Class"><code>.progress-striped</code></td>
                            		<td data-title="Effect">Used to add stripes to the progress bar.</td>
                            		<td data-title="Remark">Optional</td>
                          		</tr>
                          		<tr>
                            		<td data-title="Class"><code>.active</code></td>
                            		<td data-title="Effect">Add this class along with <code>.progress-stripped</code> to animate the progress bar.</td>
                            		<td data-title="Remark">Optional</td>
                          		</tr>
                          		<tr>
                            		<td data-title="Class"><code>.progress-bar</code></td>
                            		<td data-title="Effect">Use this class to create a stacked progress bar.</td>
                            		<td data-title="Remark">Optional</td>
                          		</tr>
                        	</tbody>
                      	</table>
        			</div>
      			</div><!-- Propeller  Class Configuration end -->
      			 
    		</div>
  		</section><!-- Configuration ends-->  		
         
	</div><!--Progressbar container end -->	 
    
</div><!--Progressbar constructor end-->

</body>
</html>

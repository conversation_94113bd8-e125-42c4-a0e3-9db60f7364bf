<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>Buttons examples - Buttons for DataTables</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Buttons example <span>Buttons for DataTables</span></h1>

			<div class="info">
				<p>The Buttons extension for DataTables provides a common set of options, API methods and styling to display buttons on a page that will interact with a DataTable.
				The core library provides the based framework upon which plug-ins can built.</p>

				<p>There are four plug-ins that are part of the core Buttons software providing various utilities:</p>

				<ul class="markdown">
					<li>HTML5 export buttons - Copy to clipboard; Save to Excel, CSV and PDF</li>
					<li>Flash export buttons - Copy to clipboard; Save to Excel, CSV and PDF for older browsers</li>
					<li>Print view</li>
					<li>Column visibility buttons</li>
				</ul>

				<p>Other extensions for DataTables also provide buttons - <a href="https://editor.datatables.net">Editor</a> for example make the <a href=
				"//editor.datatables.net/reference/button/create"><code class="button" title="Editor button type">create</code></a>, <a href=
				"//editor.datatables.net/reference/button/edit"><code class="button" title="Editor button type">edit</code></a> and <a href=
				"//editor.datatables.net/reference/button/remove"><code class="button" title="Editor button type">remove</code></a> buttons available.</p>

				<p>The examples presented here give an introduce to how Buttons can be used, the API and its various core plug-in buttons.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./column_visibility/index.html">Column Visibility</a></h3>
						<ul class="toc">
							<li><a href="./column_visibility/simple.html">Basic column visibility</a></li>
							<li><a href="./column_visibility/layout.html">Multi-column layout</a></li>
							<li><a href="./column_visibility/text.html">Internationalisation</a></li>
							<li><a href="./column_visibility/restore.html">Restore column visibility</a></li>
							<li><a href="./column_visibility/columns.html">Select columns</a></li>
							<li><a href="./column_visibility/columnsToggle.html">Visibility toggle buttons</a></li>
							<li><a href="./column_visibility/columnGroups.html">Column groups</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./flash/index.html">Flash</a></h3>
						<ul class="toc">
							<li><a href="./flash/simple.html">Flash export buttons</a></li>
							<li><a href="./flash/tsv.html">Tab separated values</a></li>
							<li><a href="./flash/filename.html">File name</a></li>
							<li><a href="./flash/copyi18n.html">Copy button internationalisation</a></li>
							<li><a href="./flash/pdfMessage.html">PDF message</a></li>
							<li><a href="./flash/pdfPage.html">Page size and orientation</a></li>
							<li><a href="./flash/hidden.html">Hidden initialisation</a></li>
							<li><a href="./flash/swfPath.html">SWF file location</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./html5/index.html">Html5</a></h3>
						<ul class="toc">
							<li><a href="./html5/simple.html">HTML5 export buttons</a></li>
							<li><a href="./html5/tsv.html">Tab separated values</a></li>
							<li><a href="./html5/filename.html">File name</a></li>
							<li><a href="./html5/copyi18n.html">Copy button internationalisation</a></li>
							<li><a href="./html5/pdfMessage.html">PDF message</a></li>
							<li><a href="./html5/pdfPage.html">PDF page size and orientation</a></li>
							<li><a href="./html5/pdfImage.html">PDF with image</a></li>
							<li><a href="./html5/pdfOpen.html">PDF - open in new window</a></li>
							<li><a href="./html5/columns.html">Column selectors</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./initialisation/index.html">Initialisation</a></h3>
						<ul class="toc">
							<li><a href="./initialisation/simple.html">Basic initialisation</a></li>
							<li><a href="./initialisation/export.html">File export</a></li>
							<li><a href="./initialisation/custom.html">Custom button</a></li>
							<li><a href="./initialisation/className.html">Class names</a></li>
							<li><a href="./initialisation/keys.html">Keyboard activation</a></li>
							<li><a href="./initialisation/collections.html">Collections</a></li>
							<li><a href="./initialisation/plugins.html">Plug-ins</a></li>
							<li><a href="./initialisation/new.html">`new` initialisation</a></li>
							<li><a href="./initialisation/multiple.html">Multiple button groups</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./print/index.html">Print</a></h3>
						<ul class="toc">
							<li><a href="./print/simple.html">Print button</a></li>
							<li><a href="./print/message.html">Custom message</a></li>
							<li><a href="./print/columns.html">Export options - column selector</a></li>
							<li><a href="./print/select.html">Export options - row selector</a></li>
							<li><a href="./print/autoPrint.html">Disable auto print</a></li>
							<li><a href="./print/customisation.html">Customisation of the print view window</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./styling/index.html">Styling</a></h3>
						<ul class="toc">
							<li><a href="./styling/bootstrap.html">Bootstrap styling</a></li>
							<li><a href="./styling/foundation.html">Foundation styling</a></li>
							<li><a href="./styling/jqueryui.html">jQuery UI styling</a></li>
						</ul>
					</div>

					<div class="toc-group">
						<h3><a href="./api/index.html">API</a></h3>
						<ul class="toc">
							<li><a href="./api/enable.html">Enable / disable</a></li>
							<li><a href="./api/text.html">Dynamic text</a></li>
							<li><a href="./api/addRemove.html">Adding and removing buttons dynamically</a></li>
							<li><a href="./api/group.html">Group selection</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extensions">extensions</a> and <a href=
					"http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
<!doctype html>
<html lang="">
<head>
 	<meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="Propeller is a front-end responsive framework based on Material design & Bootstrap.">
	<meta content="width=device-width, initial-scale=1, user-scalable=no" name="viewport">
    <title>Sidebar - Propeller Components</title>
    
	<!-- favicon --> 
	<link rel="icon" href="http://propeller.in/assets/images/favicon.ico" type="image/x-icon">
	
	<!-- Bootstrap --> 
	<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" type="text/css" rel="stylesheet" /> 
	
	<!-- Example docs (CSS for helping component example file)-->
	<link href="http://propeller.in/docs/css/example-docs.css" type="text/css" rel="stylesheet" />

	<!-- Propeller card (CSS for helping component example file) -->
	<link href="http://propeller.in/components/card/css/card.css" type="text/css" rel="stylesheet" />

	<!-- Propeller typography (CSS for helping component example file)-->
	<link href="http://propeller.in/components/typography/css/typography.css" type="text/css" rel="stylesheet" />


</head>

<body>

<!-- Sidebar -->
<div class="pmd-content pmd-content-custom" id="content" style="padding:0 20px;"> 
	
	<!-- component header -->
	<div class="componant-title-bg">
				<h1>Sidebar</h1>	
				<p class="lead">Sidebar is a narrow vertical area that is located alongside the main display area, typically containing related information or navigation options.This structure shows a responsive menu toggling system. When toggled using the button, the menu will appear/disappear.</p>
				</div><!-- component title and description end-->				 
	<!-- component header end -->		

		<div class="sidebar-pmd-content">
		<!-- basic sidebar -->
		<section class="row component-section"> 
			
			<!-- basic sidebar title and description -->
			<div class="col-md-12">
				<div id="basics">
					<h2>Simple Sidebar</h2>
				</div>
				<p>Simple Sidebar is a basic sidebar menu page layout for Bootstrap websites with off canvas navigation on smaller screen sizes. When toggled using the button, the menu will appear/disappear. When the sidebar opens, it slides in and the page content will be pushed off canvas. To create such sidebar, add <code>.pmd-sidebar-slide-push</code>.</p>				
			</div><!-- basic sidebar title and description end-->
			 
			
			<!-- basic sidebar code and example -->
			<div class="col-md-12">
				<div class="component-box"> 
					<!-- basic sidebar example -->
					<div class="row">
						<div class="col-md-12">
							<div class="pmd-card pmd-z-depth">
								<section class="nav-show" style="position: relative;">
									<iframe src="sidebar-pages/basic-sidebar-page.html" class="sidebar-iframe"></iframe>
								</section>
							</div>
						</div>
					</div><!-- basic sidebar example end -->						 
					
				</div>
			</div><!-- basic sidebar code and example end-->				 
			
		</section><!-- basic sidebar end-->
		 
		
		<!-- fixed sidebar -->
		<section class="row component-section">
		
			<!-- fixed sidebar title and description -->
			<div class="col-md-12">
				<div id="basics-fixed">
					<h2>Fixed Left Sidebar</h2>
				</div>
				<p>Fixed sidebar is the one in which, when the sidebar is open, it overlaps over the content. To create this sidebar, add <code>.pmd-sidebar-left-fixed</code>.</p>
			</div><!-- fixed sidebar title and description end -->
			
			<!-- fixed sidebar code and example -->
			<div class="col-md-12">
				<div class="component-box">
					<!-- fixed sidebar example -->
					<div class="row">
						<div class="col-md-12">
							<div class="pmd-card pmd-z-depth">
								<section class="nav-show" style="position: relative;">
									<iframe src="sidebar-pages/fixed-sidebar-page.html" class="sidebar-iframe"></iframe>
								</section>
							</div>
						</div>
					</div><!-- fixed sidebar example end-->
					
				</div>
			</div><!-- fixed sidebar code and example end-->
			
		</section><!-- fixed sidebar end-->
		
		<!-- right sidebar -->
		<section class="row component-section">
		
			<!-- right sidebar title and description -->
			<div class="col-md-12">
				<div id="right">
					<h2>Fixed Right Sidebar</h2>
				</div>
				<p>You can also create a secondary sidebar, that is, a right sidebar for tabs, palettes, or secondary actions. To create such a sidebar, add <code>.pmd-sidebar-right-fixed</code>. </p>
			</div><!-- right sidebar title and description end -->
			
			<!-- right sidebar code and example -->
			<div class="col-md-12">
				<div class="component-box">
					<!-- right sidebar example -->
					<div class="row">
						<div class="col-md-12">
							<div class="pmd-card pmd-z-depth">
								<section class="nav-show" style="position: relative;">
									<iframe src="sidebar-pages/right-sidebar-page.html" class="sidebar-iframe"></iframe>
								</section>
							</div>
						</div>
					</div> <!-- right sidebar example end -->
				
				</div>
			</div><!-- right sidebar code and example end-->
			
		</section><!-- right sidebar end --> 
		
		
		<!-- default open sidebar -->
		<section class="row component-section">
		
			<!-- default open sidebar title and description -->
			<div class="col-md-12">
				<div id="default-open">
					<h2>Default open Sidebar</h2>
					<p>On load you can show both left and right sidebars visible. Add <code>.pmd-sidebar-open</code> along with the <code>.pmd-sidebar</code> to the sidebar in order to make it open by default.</p>
				</div>
			</div><!-- default open sidebar title and description end -->
			
			<!-- default open sidebar code and example -->
			<div class="col-md-12">
				<div class="component-box">
					<!-- default open sidebar example -->
					<div class="row">
						<div class="col-md-12">
							<div class="pmd-card pmd-z-depth">
								<section class="nav-show" style="position: relative;">
									<iframe src="sidebar-pages/open-sidebar-page.html" class="sidebar-iframe"></iframe>
								</section>
							</div>
						</div>
					</div> <!-- default open sidebar example end-->
				</div>
			</div> <!-- default open sidebar code and example end -->
			
		</section> <!-- default open sidebar ends -->
		
		
		<!-- Configuration starts-->		
		<section class="row component-section">
			<div class="col-md-3">
				<div id="config">
					<h2>Configuration Options</h2>
				</div>
				<p>The Propeller CSS classes apply various predefined visual enhancements to the slider. The table lists the available classes and their effects.</p>
			</div>
			<div class="col-md-9"> 
				<!--Propeller  Class Configuration card start -->
				<div class="pmd-card pmd-table-card-responsive">
					<div class="pmd-table-card"> 
						<table class="table pmd-table table-hover">
							<thead>
								<tr>
									<th>Propeller Class</th>
									<th>Effect</th>
									<th>Remark</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td data-title="Class"><code>.pmd-sidebar</code></td>
									<td data-title="Effect">Used to hide sidebar content in desktop resolution.</td>
									<td data-title="Remark">Required</td>
								</tr>
								<tr>
									<td data-title="Class"><code>.pmd-sidebar-toggle</code></td>
									<td data-title="Effect">Used to show/hide sidebar sidebar menu.</td>
									<td data-title="Remark">Required</td>
								</tr>
								<tr>
									<td data-title="Class"><code>.pmd-sidebar-slide-push</code></td>
									<td data-title="Effect">Used to create the sidebar which when opens, it slides off the content to right.</td>
									<td data-title="Remark">Required</td>
								</tr>
								<tr>
									<td data-title="Class"><code>.pmd-sidebar-left-fixed</code></td>
									<td data-title="Effect">Used to show sidebar fixed to the left side of the window, which when opens, it overlaps over the content.</td>
									<td data-title="Remark">Optional</td>
								</tr>
								<tr>
									<td data-title="Class"><code>.pmd-sidebar-right-fixed</code></td>
									<td data-title="Effect">Used to create a right sidebar.</td>
									<td data-title="Remark">Optional</td>
								</tr>
								<tr>
									<td data-title="Class"><code>.pmd-sidebar-open</code></td>
									<td data-title="Effect">Add this class to the sidebar when you want it to be open by default.</td>
									<td data-title="Remark">Optional</td>
								</tr>
								<tr>
									<td data-title="Class"><code>.pmd-sidebar-nav</code></td>
									<td data-title="Effect">Used to show/hide user profile dropdown in navbr sidebar.</td>
									<td data-title="Remark">Optional</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div><!--Propeller  Class Configuration card end --> 
				
			</div>
		</section><!-- Configuration ends--> 
		
		</div>		
		
</div><!--Sidebar constructor end --> 

</body>
</html>

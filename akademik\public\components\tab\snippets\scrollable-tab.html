<!--Scrollable tab example -->
<div class="pmd-card pmd-z-depth"> 
	<div class="pmd-tabs pmd-tabs-scroll pmd-tabs-bg">
		<div class="pmd-tabs-scroll-left"><i class="material-icons pmd-sm">chevron_left</i></div>
		<div class="pmd-tabs-scroll-container pmd-z-depth" style="cursor: grab;">
			<div class="pmd-tab-active-bar"></div>
			<ul class="nav nav-tabs" role="tablist">
				<li role="presentation" class="active"><a href="#home-scrollable" aria-controls="home" role="tab" data-toggle="tab">Default</a></li>
				<li role="presentation"><a href="#about-scrollable" aria-controls="about" role="tab" data-toggle="tab">Fixed</a></li>
				<li role="presentation"><a href="#work-scrollable" aria-controls="work" role="tab" data-toggle="tab">Scrollable</a></li>
				<li role="presentation"><a href="#home-scrollable" aria-controls="home" role="tab" data-toggle="tab">Default</a></li>
				<li role="presentation"><a href="#about-scrollable" aria-controls="about" role="tab" data-toggle="tab">Fixed</a></li>
				<li role="presentation"><a href="#work-scrollable" aria-controls="work" role="tab" data-toggle="tab">Scrollable</a></li>
				<li role="presentation"><a href="#home-scrollable" aria-controls="home" role="tab" data-toggle="tab">Default</a></li>
				<li role="presentation"><a href="#about-scrollable" aria-controls="about" role="tab" data-toggle="tab">Fixed</a></li>
				<li role="presentation"><a href="#work-scrollable" aria-controls="work" role="tab" data-toggle="tab">Scrollable</a></li>
				<li role="presentation"><a href="#home-scrollable" aria-controls="home" role="tab" data-toggle="tab">Default</a></li>
				<li role="presentation"><a href="#about-scrollable" aria-controls="about" role="tab" data-toggle="tab">Fixed</a></li>
				<li role="presentation"><a href="#work-scrollable" aria-controls="work" role="tab" data-toggle="tab">Scrollable</a></li>
				<li role="presentation"><a href="#home-scrollable" aria-controls="home" role="tab" data-toggle="tab">Default</a></li>
				<li role="presentation"><a href="#about-scrollable" aria-controls="about" role="tab" data-toggle="tab">Fixed</a></li>
				<li role="presentation"><a href="#work-scrollable" aria-controls="work" role="tab" data-toggle="tab">Scrollable</a></li>
			</ul>
		</div>
		<div class="pmd-tabs-scroll-right"><i class="material-icons pmd-sm">chevron_right</i></div>	
	</div>
	<div class="pmd-card-body">
		<div class="tab-content">
			<div role="tabpanel" class="tab-pane active" id="home-scrollable">A tab provides the affordance for displaying grouped content. A tab label succinctly describes the tab’s associated grouping of content.</div>
			<div role="tabpanel" class="tab-pane" id="about-scrollable">Fixed tabs have equal width, calculated either as the view width divided by the number of tabs, or based on the widest tab label. To navigate between fixed tabs, touch the tab or swipe the content area left or right.</div>
			<div role="tabpanel" class="tab-pane" id="work-scrollable">To navigate between scrollable tabs, touch the tab or swipe the content area left or right. To scroll the tabs without navigating, swipe the tabs left or right.</div>
		</div>
	</div>
</div> <!--Scrollable tab example end-->
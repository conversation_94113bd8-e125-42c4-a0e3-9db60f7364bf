/*!
 * <PERSON><PERSON><PERSON> Bootstrap v3.1.0 (http://jasny.github.com/bootstrap)
 * Copyright 2011-2014 <PERSON>.
 * Licensed under Apache-2.0 (https://github.com/jasny/bootstrap/blob/master/LICENSE)
 */

.container-smooth {
  max-width: 1170px;
}
@media (min-width: 1px) {
  .container-smooth {
    width: auto;
  }
}
.btn-labeled {
  padding-top: 0;
  padding-bottom: 0;
}
.btn-label {
  position: relative;
  background: transparent;
  background: rgba(0, 0, 0, 0.15);
  display: inline-block;
  padding: 6px 12px;
  left: -12px;
  border-radius: 3px 0 0 3px;
}
.btn-label.btn-label-right {
  left: auto;
  right: -12px;
  border-radius: 0 3px 3px 0;
}
.btn-lg .btn-label {
  padding: 10px 16px;
  left: -16px;
  border-radius: 5px 0 0 5px;
}
.btn-lg .btn-label.btn-label-right {
  left: auto;
  right: -16px;
  border-radius: 0 5px 5px 0;
}
.btn-sm .btn-label {
  padding: 5px 10px;
  left: -10px;
  border-radius: 2px 0 0 2px;
}
.btn-sm .btn-label.btn-label-right {
  left: auto;
  right: -10px;
  border-radius: 0 2px 2px 0;
}
.btn-xs .btn-label {
  padding: 1px 5px;
  left: -5px;
  border-radius: 2px 0 0 2px;
}
.btn-xs .btn-label.btn-label-right {
  left: auto;
  right: -5px;
  border-radius: 0 2px 2px 0;
}
.nav-tabs-bottom {
  border-bottom: 0;
  border-top: 1px solid #dddddd;
}
.nav-tabs-bottom > li {
  margin-bottom: 0;
  margin-top: -1px;
}
.nav-tabs-bottom > li > a {
  border-radius: 0 0 4px 4px;
}
.nav-tabs-bottom > li > a:hover,
.nav-tabs-bottom > li > a:focus,
.nav-tabs-bottom > li.active > a,
.nav-tabs-bottom > li.active > a:hover,
.nav-tabs-bottom > li.active > a:focus {
  border: 1px solid #dddddd;
  border-top-color: transparent;
}
.nav-tabs-left {
  border-bottom: 0;
  border-right: 1px solid #dddddd;
}
.nav-tabs-left > li {
  margin-bottom: 0;
  margin-right: -1px;
  float: none;
}
.nav-tabs-left > li > a {
  border-radius: 4px 0 0 4px;
  margin-right: 0;
  margin-bottom: 2px;
}
.nav-tabs-left > li > a:hover,
.nav-tabs-left > li > a:focus,
.nav-tabs-left > li.active > a,
.nav-tabs-left > li.active > a:hover,
.nav-tabs-left > li.active > a:focus {
  border: 1px solid #dddddd;
  border-right-color: transparent;
}
.row > .nav-tabs-left {
  padding-right: 0;
  padding-left: 15px;
  margin-right: -1px;
  position: relative;
  z-index: 1;
}
.row > .nav-tabs-left + .tab-content {
  border-left: 1px solid #dddddd;
}
.nav-tabs-right {
  border-bottom: 0;
  border-left: 1px solid #dddddd;
}
.nav-tabs-right > li {
  margin-bottom: 0;
  margin-left: -1px;
  float: none;
}
.nav-tabs-right > li > a {
  border-radius: 0 4px 4px 0;
  margin-left: 0;
  margin-bottom: 2px;
}
.nav-tabs-right > li > a:hover,
.nav-tabs-right > li > a:focus,
.nav-tabs-right > li.active > a,
.nav-tabs-right > li.active > a:hover,
.nav-tabs-right > li.active > a:focus {
  border: 1px solid #dddddd;
  border-left-color: transparent;
}
.row > .nav-tabs-right {
  padding-left: 0;
  padding-right: 15px;
}
.navmenu,
.navbar-offcanvas {
  width: 300px;
  height: auto;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
}
.navmenu-fixed-left,
.navmenu-fixed-right,
.navbar-offcanvas {
  position: fixed;
  z-index: 1040;
  top: 0;
  bottom: 0;
  overflow-y: auto;
  border-radius: 0;
}
.navmenu-fixed-left,
.navbar-offcanvas.navmenu-fixed-left {
  left: 0;
  right: auto;
  border-width: 0 1px 0 0;
}
.navmenu-fixed-right,
.navbar-offcanvas {
  left: auto;
  right: 0;
  border-width: 0 0 0 1px;
}
.navmenu-nav {
  margin-bottom: 10px;
}
.navmenu-nav.dropdown-menu {
  position: static;
  margin: 0;
  padding-top: 0;
  float: none;
  border: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
}
.navbar-offcanvas .navbar-nav {
  margin: 0;
}
@media (min-width: 768px) {
  .navbar-offcanvas {
    width: auto;
    border-top: 0;
    box-shadow: none;
  }
  .navbar-offcanvas.offcanvas {
    position: static;
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-offcanvas .navbar-nav.navbar-left:first-child {
    margin-left: -15px;
  }
  .navbar-offcanvas .navbar-nav.navbar-right:last-child {
    margin-right: -15px;
  }
  .navbar-offcanvas .navmenu-brand {
    display: none;
  }
}
.navmenu-brand {
  display: block;
  font-size: 18px;
  line-height: 20px;
  padding: 10px 15px;
  margin: 10px 0;
}
.navmenu-brand:hover,
.navmenu-brand:focus {
  text-decoration: none;
}
.navmenu-default,
.navbar-default .navbar-offcanvas {
  background-color: #f8f8f8;
  border-color: #e7e7e7;
}
.navmenu-default .navmenu-brand,
.navbar-default .navbar-offcanvas .navmenu-brand {
  color: #777777;
}
.navmenu-default .navmenu-brand:hover,
.navbar-default .navbar-offcanvas .navmenu-brand:hover,
.navmenu-default .navmenu-brand:focus,
.navbar-default .navbar-offcanvas .navmenu-brand:focus {
  color: #5e5e5e;
  background-color: transparent;
}
.navmenu-default .navmenu-text,
.navbar-default .navbar-offcanvas .navmenu-text {
  color: #777777;
}
.navmenu-default .navmenu-nav > .dropdown > a:hover .caret,
.navbar-default .navbar-offcanvas .navmenu-nav > .dropdown > a:hover .caret,
.navmenu-default .navmenu-nav > .dropdown > a:focus .caret,
.navbar-default .navbar-offcanvas .navmenu-nav > .dropdown > a:focus .caret {
  border-top-color: #333333;
  border-bottom-color: #333333;
}
.navmenu-default .navmenu-nav > .open > a,
.navbar-default .navbar-offcanvas .navmenu-nav > .open > a,
.navmenu-default .navmenu-nav > .open > a:hover,
.navbar-default .navbar-offcanvas .navmenu-nav > .open > a:hover,
.navmenu-default .navmenu-nav > .open > a:focus,
.navbar-default .navbar-offcanvas .navmenu-nav > .open > a:focus {
  background-color: #e7e7e7;
  color: #555555;
}
.navmenu-default .navmenu-nav > .open > a .caret,
.navbar-default .navbar-offcanvas .navmenu-nav > .open > a .caret,
.navmenu-default .navmenu-nav > .open > a:hover .caret,
.navbar-default .navbar-offcanvas .navmenu-nav > .open > a:hover .caret,
.navmenu-default .navmenu-nav > .open > a:focus .caret,
.navbar-default .navbar-offcanvas .navmenu-nav > .open > a:focus .caret {
  border-top-color: #555555;
  border-bottom-color: #555555;
}
.navmenu-default .navmenu-nav > .dropdown > a .caret,
.navbar-default .navbar-offcanvas .navmenu-nav > .dropdown > a .caret {
  border-top-color: #777777;
  border-bottom-color: #777777;
}
.navmenu-default .navmenu-nav.dropdown-menu,
.navbar-default .navbar-offcanvas .navmenu-nav.dropdown-menu {
  background-color: #e7e7e7;
}
.navmenu-default .navmenu-nav.dropdown-menu > .divider,
.navbar-default .navbar-offcanvas .navmenu-nav.dropdown-menu > .divider {
  background-color: #f8f8f8;
}
.navmenu-default .navmenu-nav.dropdown-menu > .active > a,
.navbar-default .navbar-offcanvas .navmenu-nav.dropdown-menu > .active > a,
.navmenu-default .navmenu-nav.dropdown-menu > .active > a:hover,
.navbar-default .navbar-offcanvas .navmenu-nav.dropdown-menu > .active > a:hover,
.navmenu-default .navmenu-nav.dropdown-menu > .active > a:focus,
.navbar-default .navbar-offcanvas .navmenu-nav.dropdown-menu > .active > a:focus {
  background-color: #d7d7d7;
}
.navmenu-default .navmenu-nav > li > a,
.navbar-default .navbar-offcanvas .navmenu-nav > li > a {
  color: #777777;
}
.navmenu-default .navmenu-nav > li > a:hover,
.navbar-default .navbar-offcanvas .navmenu-nav > li > a:hover,
.navmenu-default .navmenu-nav > li > a:focus,
.navbar-default .navbar-offcanvas .navmenu-nav > li > a:focus {
  color: #333333;
  background-color: transparent;
}
.navmenu-default .navmenu-nav > .active > a,
.navbar-default .navbar-offcanvas .navmenu-nav > .active > a,
.navmenu-default .navmenu-nav > .active > a:hover,
.navbar-default .navbar-offcanvas .navmenu-nav > .active > a:hover,
.navmenu-default .navmenu-nav > .active > a:focus,
.navbar-default .navbar-offcanvas .navmenu-nav > .active > a:focus {
  color: #555555;
  background-color: #e7e7e7;
}
.navmenu-default .navmenu-nav > .disabled > a,
.navbar-default .navbar-offcanvas .navmenu-nav > .disabled > a,
.navmenu-default .navmenu-nav > .disabled > a:hover,
.navbar-default .navbar-offcanvas .navmenu-nav > .disabled > a:hover,
.navmenu-default .navmenu-nav > .disabled > a:focus,
.navbar-default .navbar-offcanvas .navmenu-nav > .disabled > a:focus {
  color: #cccccc;
  background-color: transparent;
}
.navmenu-inverse,
.navbar-inverse .navbar-offcanvas {
  background-color: #222222;
  border-color: #080808;
}
.navmenu-inverse .navmenu-brand,
.navbar-inverse .navbar-offcanvas .navmenu-brand {
  color: #999999;
}
.navmenu-inverse .navmenu-brand:hover,
.navbar-inverse .navbar-offcanvas .navmenu-brand:hover,
.navmenu-inverse .navmenu-brand:focus,
.navbar-inverse .navbar-offcanvas .navmenu-brand:focus {
  color: #ffffff;
  background-color: transparent;
}
.navmenu-inverse .navmenu-text,
.navbar-inverse .navbar-offcanvas .navmenu-text {
  color: #999999;
}
.navmenu-inverse .navmenu-nav > .dropdown > a:hover .caret,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .dropdown > a:hover .caret,
.navmenu-inverse .navmenu-nav > .dropdown > a:focus .caret,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .dropdown > a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.navmenu-inverse .navmenu-nav > .open > a,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .open > a,
.navmenu-inverse .navmenu-nav > .open > a:hover,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .open > a:hover,
.navmenu-inverse .navmenu-nav > .open > a:focus,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .open > a:focus {
  background-color: #080808;
  color: #ffffff;
}
.navmenu-inverse .navmenu-nav > .open > a .caret,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .open > a .caret,
.navmenu-inverse .navmenu-nav > .open > a:hover .caret,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .open > a:hover .caret,
.navmenu-inverse .navmenu-nav > .open > a:focus .caret,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .open > a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.navmenu-inverse .navmenu-nav > .dropdown > a .caret,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .dropdown > a .caret {
  border-top-color: #999999;
  border-bottom-color: #999999;
}
.navmenu-inverse .navmenu-nav.dropdown-menu,
.navbar-inverse .navbar-offcanvas .navmenu-nav.dropdown-menu {
  background-color: #080808;
}
.navmenu-inverse .navmenu-nav.dropdown-menu > .divider,
.navbar-inverse .navbar-offcanvas .navmenu-nav.dropdown-menu > .divider {
  background-color: #222222;
}
.navmenu-inverse .navmenu-nav.dropdown-menu > .active > a,
.navbar-inverse .navbar-offcanvas .navmenu-nav.dropdown-menu > .active > a,
.navmenu-inverse .navmenu-nav.dropdown-menu > .active > a:hover,
.navbar-inverse .navbar-offcanvas .navmenu-nav.dropdown-menu > .active > a:hover,
.navmenu-inverse .navmenu-nav.dropdown-menu > .active > a:focus,
.navbar-inverse .navbar-offcanvas .navmenu-nav.dropdown-menu > .active > a:focus {
  background-color: #000000;
}
.navmenu-inverse .navmenu-nav > li > a,
.navbar-inverse .navbar-offcanvas .navmenu-nav > li > a {
  color: #999999;
}
.navmenu-inverse .navmenu-nav > li > a:hover,
.navbar-inverse .navbar-offcanvas .navmenu-nav > li > a:hover,
.navmenu-inverse .navmenu-nav > li > a:focus,
.navbar-inverse .navbar-offcanvas .navmenu-nav > li > a:focus {
  color: #ffffff;
  background-color: transparent;
}
.navmenu-inverse .navmenu-nav > .active > a,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .active > a,
.navmenu-inverse .navmenu-nav > .active > a:hover,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .active > a:hover,
.navmenu-inverse .navmenu-nav > .active > a:focus,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .active > a:focus {
  color: #ffffff;
  background-color: #080808;
}
.navmenu-inverse .navmenu-nav > .disabled > a,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .disabled > a,
.navmenu-inverse .navmenu-nav > .disabled > a:hover,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .disabled > a:hover,
.navmenu-inverse .navmenu-nav > .disabled > a:focus,
.navbar-inverse .navbar-offcanvas .navmenu-nav > .disabled > a:focus {
  color: #444444;
  background-color: transparent;
}
.alert-fixed-top,
.alert-fixed-bottom {
  position: fixed;
  width: 100%;
  z-index: 1035;
  border-radius: 0;
  margin: 0;
  left: 0;
}
@media (min-width: 992px) {
  .alert-fixed-top,
  .alert-fixed-bottom {
    width: 992px;
    left: 50%;
    margin-left: -496px;
  }
}
.alert-fixed-top {
  top: 0;
  border-width: 0 0 1px 0;
}
@media (min-width: 992px) {
  .alert-fixed-top {
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    border-width: 0 1px 1px 1px;
  }
}
.alert-fixed-bottom {
  bottom: 0;
  border-width: 1px 0 0 0;
}
@media (min-width: 992px) {
  .alert-fixed-bottom {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    border-width: 1px 1px 0 1px;
  }
}
.offcanvas {
  display: none;
}
.offcanvas.in {
  display: block;
}
@media (max-width: 767px) {
  .offcanvas-xs {
    display: none;
  }
  .offcanvas-xs.in {
    display: block;
  }
}
@media (max-width: 991px) {
  .offcanvas-sm {
    display: none;
  }
  .offcanvas-sm.in {
    display: block;
  }
}
@media (max-width: 1199px) {
  .offcanvas-md {
    display: none;
  }
  .offcanvas-md.in {
    display: block;
  }
}
.offcanvas-lg {
  display: none;
}
.offcanvas-lg.in {
  display: block;
}
.canvas-sliding {
  -webkit-transition: top 0.35s, left 0.35s, bottom 0.35s, right 0.35s;
  transition: top 0.35s, left 0.35s, bottom 0.35s, right 0.35s;
}
.offcanvas-clone {
  height: 0px !important;
  width: 0px !important;
  overflow: hidden !important;
  border: none !important;
  margin: 0px !important;
  padding: 0px !important;
  position: absolute !important;
  top: auto !important;
  left: auto !important;
  bottom: 0px !important;
  right: 0px !important;
  opacity: 0 !important;
}
.table.rowlink td:not(.rowlink-skip),
.table .rowlink td:not(.rowlink-skip) {
  cursor: pointer;
}
.table.rowlink td:not(.rowlink-skip) a,
.table .rowlink td:not(.rowlink-skip) a {
  color: inherit;
  font: inherit;
  text-decoration: inherit;
}
.table-hover.rowlink tr:hover td,
.table-hover .rowlink tr:hover td {
  background-color: #cfcfcf;
}
.btn-file {
  overflow: hidden;
  position: relative;
  vertical-align: middle;
}
.btn-file > input {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  font-size: 23px;
  height: 100%;
  width: 100%;
  direction: ltr;
  cursor: pointer;
}
.fileinput {
  margin-bottom: 9px;
  display: inline-block;
}
.fileinput .form-control {
  padding-top: 7px;
  padding-bottom: 5px;
  display: inline-block;
  margin-bottom: 0px;
  vertical-align: middle;
  cursor: text;
}
.fileinput .thumbnail {
  overflow: hidden;
  display: inline-block;
  margin-bottom: 5px;
  vertical-align: middle;
  text-align: center;
}
.fileinput .thumbnail > img {
  max-height: 100%;
}
.fileinput .btn {
  vertical-align: middle;
}
.fileinput-exists .fileinput-new,
.fileinput-new .fileinput-exists {
  display: none;
}
.fileinput-inline .fileinput-controls {
  display: inline;
}
.fileinput-filename {
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
}
.form-control .fileinput-filename {
  vertical-align: bottom;
}
.fileinput.input-group {
  display: table;
}
.fileinput.input-group > * {
  position: relative;
  z-index: 2;
}
.fileinput.input-group > .btn-file {
  z-index: 1;
}
.fileinput-new.input-group .btn-file,
.fileinput-new .input-group .btn-file {
  border-radius: 0 4px 4px 0;
}
.fileinput-new.input-group .btn-file.btn-xs,
.fileinput-new .input-group .btn-file.btn-xs,
.fileinput-new.input-group .btn-file.btn-sm,
.fileinput-new .input-group .btn-file.btn-sm {
  border-radius: 0 3px 3px 0;
}
.fileinput-new.input-group .btn-file.btn-lg,
.fileinput-new .input-group .btn-file.btn-lg {
  border-radius: 0 6px 6px 0;
}
.form-group.has-warning .fileinput .fileinput-preview {
  color: #8a6d3b;
}
.form-group.has-warning .fileinput .thumbnail {
  border-color: #faebcc;
}
.form-group.has-error .fileinput .fileinput-preview {
  color: #a94442;
}
.form-group.has-error .fileinput .thumbnail {
  border-color: #ebccd1;
}
.form-group.has-success .fileinput .fileinput-preview {
  color: #3c763d;
}
.form-group.has-success .fileinput .thumbnail {
  border-color: #d6e9c6;
}
.input-group-addon:not(:first-child) {
  border-left: 0;
}


.btn.btn-file{overflow:hidden;position:relative;vertical-align:middle; min-width:36px; padding:5px 5px 4px;}
.btn.btn-file .dic{font-size:20px;}
.btn.btn-file > input[type=file]{width:100%;position:absolute;left:0;top:0;opacity:0;cursor:pointer}
.fileupload .uneditable-input{display:inline-block;margin-bottom:0;vertical-align:middle;height:28px !important}
.fileupload .thumbnail{overflow:hidden;display:inline-block;margin-bottom:10px;vertical-align:middle;text-align:center}
.fileupload .thumbnail > img{display:inline-block;vertical-align:middle;max-height:100%}
.fileupload .btn{vertical-align:middle}
.fileupload-exists .fileupload-new,.fileupload-new .fileupload-exists{display:none}
.fileupload-inline .fileupload-controls{display:inline}
.fileupload-new .input-append .btn-file{-webkit-border-radius:0 3px 3px 0;-moz-border-radius:0 3px 3px 0;border-radius:0 3px 3px 0}
.fileupload .fileupload-preview {vertical-align:middle}
.fileupload .close.fileupload-exists {vertical-align:middle}
.btn-file .ripple-wrapper{ display:none;}
.action-button{opacity:0;position:absolute; bottom:15px; left:0px; text-align:center; width:100%;-webkit-transition: 0.4s ease-in-out;-moz-transition: 0.4s ease-in-out;-o-transition: 0.4s ease-in-out;transition: 0.4s ease-in-out;}
.fileinput{width:166px;-webkit-transition: 0.4s ease-in-out;overflow:hidden;-moz-transition: 0.4s ease-in-out;-o-transition: 0.4s ease-in-out;transition: 0.4s ease-in-out; margin-right:10px;}
.fileinput:hover .action-button{
	opacity:1;
}
.fileupload .btn{ padding:5px 10px; min-width:36px; background-color:#FFF !important; font-size:15px;}
.prousername{margin-bottom:20px;}
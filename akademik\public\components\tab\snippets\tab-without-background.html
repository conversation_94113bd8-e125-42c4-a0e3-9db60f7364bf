<!--without background tab example -->
<div class="pmd-card pmd-z-depth"> 
	<div class="pmd-tabs">
		<div class="pmd-tab-active-bar"></div>
		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#default" aria-controls="default" role="tab" data-toggle="tab">Default</a></li>
			<li role="presentation"><a href="#fixed" aria-controls="fixed" role="tab" data-toggle="tab">Fixed</a></li>
			<li role="presentation"><a href="#scrollable" aria-controls="scrollable" role="tab" data-toggle="tab">Scrollable</a></li>
		</ul>
	</div>
	<div class="pmd-card-body">
		<div class="tab-content">
			<div role="tabpanel" class="tab-pane active" id="default">A tab provides the affordance for displaying grouped content. A tab label succinctly describes the tab’s associated grouping of content.</div>
			<div role="tabpanel" class="tab-pane" id="fixed">Fixed tabs have equal width, calculated either as the view width divided by the number of tabs, or based on the widest tab label. To navigate between fixed tabs, touch the tab or swipe the content area left or right.</div>
			<div role="tabpanel" class="tab-pane" id="scrollable">To navigate between scrollable tabs, touch the tab or swipe the content area left or right. To scroll the tabs without navigating, swipe the tabs left or right.</div>
		</div>
	</div>
</div> <!--without background tab example end -->
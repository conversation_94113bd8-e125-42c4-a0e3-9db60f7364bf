<!-- Display an information alert at the top-left position of the window -->
<button type="button" data-positionX="left" data-positionY="top" data-effect="fadeInUp" data-message="Heads up! This alert needs your attention, but it's not super important." data-type="information" class="btn pmd-ripple-effect btn-info pmd-btn-raised pmd-alert-toggle">Alert Information</button>
							
<!-- Display a warning alert at the top-center position of the window -->
<button type="button" data-positionX="center" data-positionY="top" data-effect="fadeInUp" data-message=" Warning! Spyware detected on your system." data-type="warning" class="btn pmd-ripple-effect btn-warning pmd-z-depth pmd-alert-toggle">Alert Warning</button>

<!-- Display a success alert at the top-right position of the window -->
<button type="button" data-positionX="right" data-positionY="top" data-effect="fadeInUp" data-message="Well done! You successfully read this important alert message." data-type="success" class="btn pmd-ripple-effect btn-success pmd-z-depth pmd-alert-toggle">Alert Success</button>

<!-- Display an error alert at the top-right position of the window -->
<button type="button" data-positionX="right" data-positionY="top" data-effect="fadeInUp" data-message="Oh snap! Change a few things up and try submitting again." data-type="error" class="btn pmd-ripple-effect btn-danger pmd-z-depth pmd-alert-toggle">Alert Error</button>

<!--Display <PERSON><PERSON> at the top-left position of the window with a close action -->
<button type="button"  data-positionX="left" data-positionY="top" data-effect="fadeInUp" data-action="true" data-action-text="Close" data-message="You have 1 messages" class="btn pmd-ripple-effect btn-default pmd-btn-raised pmd-alert-toggle">Top Left</button>

<!--Display <PERSON><PERSON> at the top-center position of the window with a refresh action -->
<button type="button" data-positionX="center" data-positionY="top" data-effect="fadeInUp" data-action="true" data-action-text="Refresh" data-message="You have 2 messages" class="btn pmd-ripple-effect btn-default pmd-btn-raised pmd-alert-toggle">Top Center</button>

<!--Display Alert at the top-right position of the window with an Ok action -->
<button type="button" data-positionX="right" data-positionY="top" data-effect="fadeInUp" data-action="true" data-action-text="Ok" data-message="You have 3 messages" class="btn pmd-ripple-effect btn-default pmd-btn-raised pmd-alert-toggle"> Top Right</button>

<!-- Display Alert at the bottom-left position of the window  -->
<button type="button" data-positionX="left" data-positionY="bottom" data-effect="fadeInDown" data-action="true" data-message="You have 4 messages" class="btn pmd-ripple-effect btn-default pmd-btn-raised pmd-alert-toggle">Bottom Left</button>
<!-- Display Alert at the bottom-center position of the window -->
<button type="button" data-positionX="center" data-positionY="bottom" data-effect="fadeInDown" data-action="true" data-message="You have 5 messages" class="btn pmd-ripple-effect btn-default pmd-btn-raised pmd-alert-toggle">Bottom Center</button>
<!-- Display Alert at the bottom-right position of the window -->
<button type="button" data-positionX="right" data-positionY="bottom" data-effect="fadeInDown" data-action="true" data-message="You have 6 messages" class="btn pmd-ripple-effect btn-default pmd-btn-raised pmd-alert-toggle">Bottom Right</button>

